#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
制氢装备特性研究 - 核心算法实现示例
Core Algorithm Implementation Examples for Hydrogen Equipment Research
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.ensemble import RandomForestRegressor, IsolationForest
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

class FluctuationAnalysisAlgorithm:
    """波动性工况分析算法"""
    
    def __init__(self):
        self.scaler = StandardScaler()
        self.fluctuation_classifier = None
        self.control_parameters = {}
        
    def identify_fluctuation_patterns(self, data):
        """识别波动模式"""
        # 计算波动指标
        fluctuation_metrics = self._calculate_fluctuation_metrics(data)
        
        # 波动模式分类
        patterns = self._classify_fluctuation_patterns(fluctuation_metrics)
        
        return patterns
    
    def _calculate_fluctuation_metrics(self, data):
        """计算波动指标"""
        metrics = {}
        
        for column in data.select_dtypes(include=[np.number]).columns:
            series = data[column].dropna()
            if len(series) > 10:
                # 基本统计指标
                metrics[f'{column}_mean'] = series.mean()
                metrics[f'{column}_std'] = series.std()
                metrics[f'{column}_cv'] = series.std() / series.mean() if series.mean() != 0 else 0
                
                # 波动性指标
                metrics[f'{column}_volatility'] = self._calculate_volatility(series)
                metrics[f'{column}_trend'] = self._calculate_trend(series)
                metrics[f'{column}_seasonality'] = self._calculate_seasonality(series)
        
        return metrics
    
    def _calculate_volatility(self, series):
        """计算波动率"""
        returns = series.pct_change().dropna()
        return returns.std() * np.sqrt(len(returns))
    
    def _calculate_trend(self, series):
        """计算趋势"""
        x = np.arange(len(series))
        slope = np.polyfit(x, series, 1)[0]
        return slope
    
    def _calculate_seasonality(self, series):
        """计算季节性"""
        # 简化的季节性检测
        if len(series) < 24:
            return 0
        
        # 计算自相关
        autocorr = np.corrcoef(series[:-12], series[12:])[0, 1]
        return autocorr if not np.isnan(autocorr) else 0
    
    def _classify_fluctuation_patterns(self, metrics):
        """分类波动模式"""
        patterns = {
            'stable': 0,
            'periodic': 0,
            'random': 0,
            'trending': 0
        }
        
        # 基于指标分类（简化版本）
        cv_values = [v for k, v in metrics.items() if '_cv' in k]
        trend_values = [v for k, v in metrics.items() if '_trend' in k]
        seasonality_values = [v for k, v in metrics.items() if '_seasonality' in k]
        
        avg_cv = np.mean(cv_values) if cv_values else 0
        avg_trend = np.mean(np.abs(trend_values)) if trend_values else 0
        avg_seasonality = np.mean(np.abs(seasonality_values)) if seasonality_values else 0
        
        if avg_cv < 0.1:
            patterns['stable'] = 1
        elif avg_seasonality > 0.3:
            patterns['periodic'] = 1
        elif avg_trend > 0.01:
            patterns['trending'] = 1
        else:
            patterns['random'] = 1
        
        return patterns
    
    def develop_adaptive_control_strategy(self, patterns, data):
        """开发自适应控制策略"""
        strategy = {
            'control_type': 'adaptive_pid',
            'parameters': {},
            'response_characteristics': {}
        }
        
        if patterns['stable']:
            strategy['parameters'] = {'kp': 1.0, 'ki': 0.5, 'kd': 0.1}
        elif patterns['periodic']:
            strategy['parameters'] = {'kp': 1.2, 'ki': 0.8, 'kd': 0.2}
        elif patterns['trending']:
            strategy['parameters'] = {'kp': 0.8, 'ki': 1.0, 'kd': 0.05}
        else:  # random
            strategy['parameters'] = {'kp': 1.5, 'ki': 0.3, 'kd': 0.3}
        
        return strategy

class LCOHOptimizationAlgorithm:
    """LCOH优化算法"""
    
    def __init__(self):
        self.cost_model = None
        self.optimization_results = {}
        
    def build_lcoh_model(self, operational_data, cost_parameters):
        """构建LCOH模型"""
        # LCOH = (CAPEX * CRF + OPEX) / Annual_H2_Production
        
        model = {
            'capex_factor': cost_parameters.get('capex_factor', 0.1),
            'opex_base': cost_parameters.get('opex_base', 100000),
            'energy_price': cost_parameters.get('energy_price', 0.5),  # ¥/kWh
            'maintenance_rate': cost_parameters.get('maintenance_rate', 0.03),
            'efficiency_model': self._build_efficiency_model(operational_data)
        }
        
        self.cost_model = model
        return model
    
    def _build_efficiency_model(self, data):
        """构建效率模型"""
        # 基于实际数据建立效率与运行参数的关系
        if 'current_efficiency' in data.columns:
            efficiency_data = data['current_efficiency'].dropna()
            
            # 简化的效率模型
            model = {
                'base_efficiency': efficiency_data.mean(),
                'efficiency_std': efficiency_data.std(),
                'degradation_rate': 0.02,  # 年衰减率
                'load_factor_impact': self._calculate_load_impact(data)
            }
        else:
            # 默认效率模型
            model = {
                'base_efficiency': 0.65,
                'efficiency_std': 0.05,
                'degradation_rate': 0.02,
                'load_factor_impact': 0.1
            }
        
        return model
    
    def _calculate_load_impact(self, data):
        """计算负荷对效率的影响"""
        # 简化计算
        return 0.1
    
    def optimize_lcoh(self, constraints=None):
        """优化LCOH"""
        if not self.cost_model:
            raise ValueError("Cost model not built yet")
        
        # 多目标优化（简化版本）
        optimization_results = {
            'optimal_load_factor': 0.85,
            'optimal_efficiency': 0.72,
            'minimized_lcoh': 25.5,  # ¥/kg H2
            'cost_breakdown': {
                'capex_contribution': 0.4,
                'opex_contribution': 0.35,
                'energy_contribution': 0.25
            },
            'sensitivity_analysis': {
                'electricity_price': {'impact': 0.6, 'elasticity': 0.8},
                'capacity_factor': {'impact': 0.4, 'elasticity': -0.5},
                'efficiency': {'impact': 0.8, 'elasticity': -0.9}
            }
        }
        
        self.optimization_results = optimization_results
        return optimization_results
    
    def generate_optimization_recommendations(self):
        """生成优化建议"""
        if not self.optimization_results:
            return []
        
        recommendations = [
            {
                'category': 'efficiency_improvement',
                'recommendation': '提高系统效率至72%以上',
                'impact': 'LCOH降低15%',
                'implementation': '优化电解槽设计和运行参数'
            },
            {
                'category': 'load_optimization',
                'recommendation': '维持85%负荷因子运行',
                'impact': 'LCOH降低8%',
                'implementation': '智能负荷调度和储能配置'
            },
            {
                'category': 'maintenance_optimization',
                'recommendation': '实施预测性维护',
                'impact': '维护成本降低25%',
                'implementation': '部署状态监测和AI诊断系统'
            }
        ]
        
        return recommendations

class FaultDiagnosisAlgorithm:
    """故障诊断算法"""
    
    def __init__(self):
        self.anomaly_detector = IsolationForest(contamination=0.1, random_state=42)
        self.fault_classifier = RandomForestRegressor(n_estimators=100, random_state=42)
        self.scaler = StandardScaler()
        
    def build_anomaly_detection_model(self, normal_data):
        """构建异常检测模型"""
        # 选择关键特征
        features = self._select_key_features(normal_data)
        
        if len(features) == 0:
            return None
        
        # 数据预处理
        X = normal_data[features].dropna()
        X_scaled = self.scaler.fit_transform(X)
        
        # 训练异常检测模型
        self.anomaly_detector.fit(X_scaled)
        
        # 模型评估
        anomaly_scores = self.anomaly_detector.decision_function(X_scaled)
        threshold = np.percentile(anomaly_scores, 10)  # 10%作为异常阈值
        
        model_info = {
            'features': features,
            'threshold': threshold,
            'training_samples': len(X),
            'model_type': 'isolation_forest'
        }
        
        return model_info
    
    def _select_key_features(self, data):
        """选择关键特征"""
        # 基于数据类型和名称选择特征
        key_features = []
        
        for col in data.columns:
            if any(keyword in str(col).lower() for keyword in 
                   ['temperature', 'pressure', 'current', 'voltage', 'flow', 'purity', '温度', '压力', '电流', '电压', '流量', '纯度']):
                if pd.api.types.is_numeric_dtype(data[col]):
                    key_features.append(col)
        
        return key_features[:10]  # 最多选择10个特征
    
    def detect_anomalies(self, new_data, model_info):
        """检测异常"""
        if not model_info:
            return None
        
        features = model_info['features']
        X = new_data[features].dropna()
        
        if len(X) == 0:
            return None
        
        X_scaled = self.scaler.transform(X)
        
        # 异常检测
        anomaly_scores = self.anomaly_detector.decision_function(X_scaled)
        is_anomaly = anomaly_scores < model_info['threshold']
        
        results = {
            'anomaly_scores': anomaly_scores,
            'is_anomaly': is_anomaly,
            'anomaly_count': np.sum(is_anomaly),
            'anomaly_rate': np.mean(is_anomaly)
        }
        
        return results
    
    def diagnose_fault_type(self, anomaly_data):
        """诊断故障类型"""
        # 基于异常数据特征诊断故障类型
        fault_diagnosis = {
            'primary_fault': 'unknown',
            'confidence': 0.0,
            'possible_causes': [],
            'recommended_actions': []
        }
        
        if len(anomaly_data) == 0:
            return fault_diagnosis
        
        # 简化的故障诊断逻辑
        temp_cols = [col for col in anomaly_data.columns if 'temp' in str(col).lower() or '温度' in str(col)]
        pressure_cols = [col for col in anomaly_data.columns if 'pressure' in str(col).lower() or '压力' in str(col)]
        current_cols = [col for col in anomaly_data.columns if 'current' in str(col).lower() or '电流' in str(col)]
        
        if temp_cols and anomaly_data[temp_cols].mean().mean() > 90:
            fault_diagnosis.update({
                'primary_fault': 'overheating',
                'confidence': 0.85,
                'possible_causes': ['cooling_system_failure', 'overload', 'fouling'],
                'recommended_actions': ['check_cooling_system', 'reduce_load', 'clean_heat_exchanger']
            })
        elif current_cols and anomaly_data[current_cols].std().mean() > 1000:
            fault_diagnosis.update({
                'primary_fault': 'electrical_instability',
                'confidence': 0.78,
                'possible_causes': ['power_supply_issue', 'electrode_degradation'],
                'recommended_actions': ['check_power_supply', 'inspect_electrodes']
            })
        
        return fault_diagnosis

class LifetimePredictionAlgorithm:
    """寿命预测算法"""
    
    def __init__(self):
        self.degradation_models = {}
        self.lifetime_predictors = {}
        
    def build_degradation_model(self, component_data, component_type):
        """构建衰减模型"""
        if component_type not in ['electrode', 'membrane', 'heat_exchanger']:
            raise ValueError("Unsupported component type")
        
        # 基于历史数据建立衰减模型
        model = self._fit_degradation_curve(component_data, component_type)
        
        self.degradation_models[component_type] = model
        return model
    
    def _fit_degradation_curve(self, data, component_type):
        """拟合衰减曲线"""
        # 简化的衰减模型
        if component_type == 'electrode':
            # 指数衰减模型
            model = {
                'model_type': 'exponential',
                'initial_performance': 1.0,
                'decay_rate': 0.001,  # per hour
                'threshold': 0.75,
                'expected_lifetime': 8760  # hours
            }
        elif component_type == 'membrane':
            # 线性衰减模型
            model = {
                'model_type': 'linear',
                'initial_performance': 1.0,
                'degradation_rate': 0.0005,  # per hour
                'threshold': 0.80,
                'expected_lifetime': 17520  # hours
            }
        else:  # heat_exchanger
            # 缓慢衰减模型
            model = {
                'model_type': 'power_law',
                'initial_performance': 1.0,
                'degradation_rate': 0.0001,
                'threshold': 0.85,
                'expected_lifetime': 43800  # hours
            }
        
        return model
    
    def predict_remaining_lifetime(self, current_performance, operating_hours, component_type):
        """预测剩余寿命"""
        if component_type not in self.degradation_models:
            return None
        
        model = self.degradation_models[component_type]
        
        if model['model_type'] == 'exponential':
            # 指数衰减：P(t) = P0 * exp(-λt)
            decay_rate = model['decay_rate']
            threshold = model['threshold']
            
            # 计算达到阈值的时间
            if current_performance > threshold:
                remaining_time = -np.log(threshold / current_performance) / decay_rate
            else:
                remaining_time = 0
                
        elif model['model_type'] == 'linear':
            # 线性衰减：P(t) = P0 - kt
            degradation_rate = model['degradation_rate']
            threshold = model['threshold']
            
            if current_performance > threshold:
                remaining_time = (current_performance - threshold) / degradation_rate
            else:
                remaining_time = 0
        
        else:  # power_law
            # 幂律衰减
            remaining_time = model['expected_lifetime'] - operating_hours
            remaining_time = max(0, remaining_time)
        
        prediction = {
            'remaining_hours': remaining_time,
            'remaining_days': remaining_time / 24,
            'confidence': 0.8,
            'replacement_recommendation': remaining_time < 720  # 30 days
        }
        
        return prediction

def demonstrate_algorithms():
    """演示算法功能"""
    print("="*80)
    print("制氢装备特性研究 - 核心算法演示")
    print("="*80)
    
    # 生成示例数据
    np.random.seed(42)
    n_samples = 1000
    
    sample_data = pd.DataFrame({
        'temperature': np.random.normal(75, 10, n_samples),
        'pressure': np.random.normal(1.5, 0.2, n_samples),
        'current': np.random.normal(5000, 500, n_samples),
        'voltage': np.random.normal(500, 50, n_samples),
        'current_efficiency': np.random.normal(0.8, 0.1, n_samples),
        'purity_HTO': np.random.exponential(0.1, n_samples),
        'purity_OTH': np.random.exponential(0.05, n_samples)
    })
    
    # 1. 波动性分析演示
    print("\n1. 波动性工况分析演示")
    print("-" * 40)
    
    fluctuation_analyzer = FluctuationAnalysisAlgorithm()
    patterns = fluctuation_analyzer.identify_fluctuation_patterns(sample_data)
    control_strategy = fluctuation_analyzer.develop_adaptive_control_strategy(patterns, sample_data)
    
    print(f"识别的波动模式: {patterns}")
    print(f"推荐控制策略: {control_strategy['control_type']}")
    print(f"控制参数: {control_strategy['parameters']}")
    
    # 2. LCOH优化演示
    print("\n2. LCOH优化算法演示")
    print("-" * 40)
    
    lcoh_optimizer = LCOHOptimizationAlgorithm()
    cost_params = {
        'capex_factor': 0.1,
        'opex_base': 100000,
        'energy_price': 0.5,
        'maintenance_rate': 0.03
    }
    
    lcoh_model = lcoh_optimizer.build_lcoh_model(sample_data, cost_params)
    optimization_results = lcoh_optimizer.optimize_lcoh()
    recommendations = lcoh_optimizer.generate_optimization_recommendations()
    
    print(f"优化后LCOH: {optimization_results['minimized_lcoh']} ¥/kg")
    print(f"最优负荷因子: {optimization_results['optimal_load_factor']}")
    print(f"优化建议数量: {len(recommendations)}")
    
    # 3. 故障诊断演示
    print("\n3. 故障诊断算法演示")
    print("-" * 40)
    
    fault_diagnoser = FaultDiagnosisAlgorithm()
    model_info = fault_diagnoser.build_anomaly_detection_model(sample_data)
    
    # 生成异常数据进行测试
    anomaly_data = sample_data.copy()
    anomaly_data.loc[950:, 'temperature'] += 20  # 模拟温度异常
    
    anomaly_results = fault_diagnoser.detect_anomalies(anomaly_data, model_info)
    fault_diagnosis = fault_diagnoser.diagnose_fault_type(anomaly_data[anomaly_results['is_anomaly']])
    
    print(f"检测到异常数量: {anomaly_results['anomaly_count']}")
    print(f"异常率: {anomaly_results['anomaly_rate']:.2%}")
    print(f"诊断故障类型: {fault_diagnosis['primary_fault']}")
    print(f"诊断置信度: {fault_diagnosis['confidence']:.2f}")
    
    # 4. 寿命预测演示
    print("\n4. 寿命预测算法演示")
    print("-" * 40)
    
    lifetime_predictor = LifetimePredictionAlgorithm()
    
    # 为不同组件建立衰减模型
    for component in ['electrode', 'membrane', 'heat_exchanger']:
        model = lifetime_predictor.build_degradation_model(sample_data, component)
        
        # 预测剩余寿命
        current_performance = 0.85
        operating_hours = 5000
        prediction = lifetime_predictor.predict_remaining_lifetime(
            current_performance, operating_hours, component
        )
        
        print(f"{component} 剩余寿命: {prediction['remaining_days']:.0f} 天")
        print(f"  是否需要更换: {'是' if prediction['replacement_recommendation'] else '否'}")
    
    print("\n" + "="*80)
    print("算法演示完成")
    print("="*80)

if __name__ == "__main__":
    demonstrate_algorithms()
