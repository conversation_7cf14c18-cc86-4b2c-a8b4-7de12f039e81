import os,gzip,pickle
from keys import Cols, DataDir
from utils import *

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import matplotlib.cm as cm
import matplotlib.colors as mcolors
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from warnings import filterwarnings

# 中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
# 过滤警报
filterwarnings('ignore')

source_folder = os.path.join(
    DataDir.raw,
    '大唐多伦2000运行数据记录'
)
target_folder = os.path.join(
    DataDir.interim,
    '大唐多伦2000运行数据记录'
)
if not os.path.exists(target_folder):
    os.makedirs(target_folder)
file_list = os.listdir(source_folder)
filtered_list = [item for item in file_list if not item.startswith('~$')]
filtered_list = [item for item in file_list if not 'gzip' in item]
print('当前文件夹：'+source_folder+'，包含文件夹：')
filtered_list


# 找到时间列（假设时间列名为 'time'）
def find_time_column(df):
    for col in df.columns:
        if pd.api.types.is_datetime64_any_dtype(df[col]):
            return col
    return None
import pandas as pd

def merge_dataframes(df_list):
    # 按列名分组 DataFrame
    column_groups = {}
    for df in df_list:
        column_tuple = tuple(sorted(df.columns))
        if column_tuple not in column_groups:
            column_groups[column_tuple] = []
        column_groups[column_tuple].append(df)

    # 合并列名相同的 DataFrame
    merged_same_columns = []
    for column_group in column_groups.values():
        if len(column_group) > 1:
            # 按索引排序并合并
            merged = pd.concat(column_group, sort=False).sort_index()
            merged_same_columns.append(merged)
        else:
            merged_same_columns.append(column_group[0])


    # 对剩余的 DataFrame 按照时间进行 merge_asof
    final_result = None
    for df in merged_same_columns:
        time_col = find_time_column(df)
        if time_col is None:
            raise ValueError("No datetime column found in DataFrame.")
        df = df.sort_values(by=time_col)
        if final_result is None:
            final_result = df
        else:
            final_result = pd.merge_asof(final_result, df, on=time_col, direction='nearest')

    return final_result


list_df = []
for file in filtered_list:
    df_cur = pd.read_excel(
        os.path.join(
            source_folder,
            file
        )
    )
    df_cur = df_cur.rename(
        columns = {
            '时间\n':Cols.date_time
        }
    )
    df_cur[Cols.date_time] = df_cur[Cols.date_time].apply(pd.to_datetime)
    # 定义列名列表
    columns = df_cur.columns
    # 使用str.replace()方法替换换行符
    new_columns = columns.str.replace('\n', '')
    with gzip.open(
        os.path.join(
            source_folder,
            file.split('.xlsx')[0] + ' gzip'
        ),
        'wb'
    ) as f:
        pickle.dump(df_cur,f)
    df_cur.columns = new_columns
    list_df.append(df_cur)
df_list = list_df
column_groups = {}
for df in df_list:
    column_tuple = tuple(sorted(df.columns))
    if column_tuple not in column_groups:
        column_groups[column_tuple] = []
    column_groups[column_tuple].append(df)


# 合并列名相同的 DataFrame
merged_same_columns = []
for column_group in column_groups.values():
    if len(column_group) > 1:
        # 按索引排序并合并
        merged = pd.concat(column_group, sort=False).sort_index()
        merged_same_columns.append(merged)
    else:
        merged_same_columns.append(column_group[0])

for item in merged_same_columns:
    print(item.columns)

df.columns








# 对剩余的 DataFrame 按照时间进行 merge_asof
final_result = None
for df in merged_same_columns:
    time_col = find_time_column(df)
    df = df.drop(columns=['序号'])
    if time_col is None:
        raise ValueError("No datetime column found in DataFrame.")
    df = df.sort_values(by=time_col)
    if final_result is None:
        final_result = df
    else:
        final_result = pd.merge_asof(final_result, df, on=time_col, direction='nearest')
df_res = final_result
for col in df_res.columns[1:]:
    df_res[col] = df_res[col].replace('--',np.nan)
    df_res[col] = df_res[col].ffill().fillna(0)
    df_res[col] = df_res[col].apply(float)

df_res.columns

df_res[Cols.lye_flow] = df_res[['FT_0201D.PV碱液流量(外部2线制无源4-20ma，配隔离器）_y','FT_0201D.PV碱液流量(外部2线制无源4-20ma，配隔离器）_x']].mean()
df_res[Cols.lye_temp] = df_res[['TT_0201D.PV碱液冷却器碱液出口温(外部2线制无源4-20ma，配隔离器）_y','TT_0201D.PV碱液冷却器碱液出口温(外部2线制无源4-20ma，配隔离器）_x']].max(axis=1)
df_res[Cols.HTO] = df_res[['AT_0201D.PV氧中氢含量1(外部4线制有源4-20mA,AI及DC24V受DO通/断，配隔离器）','AT_0204D.PV氧中氢含量2(外部4线制有源4-20mA,AI及DC24V受DO通/断，配隔离器）',]].max(axis=1)
df_res[Cols.OTH] = df_res[['AT_0202D.PV氢中氧含量1(外部4线制有源4-20mA,AI及DC24V受DO通/断，配隔离器）','AT_0203D.PV氢中氧含量2(外部4线制有源4-20mA,AI及DC24V受DO通/断，配隔离器）',]].max(axis=1)
rename_cols = {
    'II_ZLG_X0101D.PV整流柜X0101D电流反馈':Cols.current, 
    'EI_ZLG_X0101D.PV整流柜X0101D电压反馈':Cols.voltage,
    'TT_0101D.PV电解槽氧槽温(外部2线制无源4-20ma，配隔离器）':Cols.temp_O,
    'TT_0104D.PV电解槽氢槽温(外部2线制无源4-20ma，配隔离器）':Cols.temp_H,
    'FT_0202D.PV氢气出口流量(外部4线制有源4-20mA，配隔离器）':'hydrogen_flow_separator',
    'FT_0302D.PV产品气流量':'hydrogen_flow_purify',
}
df_res = df_res.rename(columns = rename_cols)
df_res[Cols.cell_voltage] =( df_res[Cols.voltage]+15)/346
df_res[Cols.current_density] = df_res[Cols.current]/(np.pi*(2.2/2)**2)
df_res[Cols.hydrogen_flow_cal] = df_res[Cols.current] * 346 / 2390

# 预存储合并后结果
with gzip.open(
    os.path.join(
        target_folder,
        'G2000-大唐_202500226 gzip'
    ),
    'wb'

) as f:
    pickle.dump(df_res,f)

df_res.to_excel(
    os.path.join(
        target_folder,
        'G2000-大唐_202500226.xlsx'
    )
)



source_folder = os.path.join(
    DataDir.raw,
    '大唐多伦数据记录'
)
target_folder = os.path.join(
    DataDir.interim,
    '大唐多伦数据记录'
)
if not os.path.exists(target_folder):
    os.makedirs(target_folder)
file_list = os.listdir(source_folder)
filtered_list = [item for item in file_list if not item.startswith('~$')]
print('当前文件夹：'+source_folder+'，包含文件夹：')
filtered_list

for folder in filtered_list:
    cur_folder = os.path.join(source_folder,folder)
    print(folder)
    file_list = os.listdir(cur_folder)
    for file in file_list:
        print('--'+file)

folder = '隆基运行数据'
cur_folder = os.path.join(source_folder,folder)
print(folder)
file_list = os.listdir(cur_folder)

col_set = set()
df_list = []
for file in file_list:
    if '.xlsx' in file:
        df_cur = pd.read_excel(
            os.path.join(
                cur_folder,
                file
            )
        )
        for col in df_cur.columns:
            col_set.add(col)
        
        df_list.append(df_cur)

df_res=df_list[0].drop(columns = ['序号\n'])
for df in df_list[1:]:
    df_res = pd.merge(
        left = df_res,
        right = df.drop(columns =[ '序号\n']),
        how = 'outer',
        on = '时间\n'
    )

# 预存储合并后结果
with gzip.open(
    os.path.join(
        cur_folder,
        '隆基 all records gzip'
    ),
    'wb'

) as f:
    pickle.dump(df_res,f)

df_res.columns

df_all = df_res.rename(
    columns = {
        '时间\n':Cols.date_time,
        'AT_0203C.PV\n氢中氧含量2(外部4线制有源4-20mA,AI及DC24V受DO通/断，配隔离器）':Cols.OTH_a,
        'AT_0201C.PV\n氧中氢含量1(外部4线制有源4-20mA,AI及DC24V受DO通/断，配隔离器）':Cols.HTO_a,
        'AT_0202C.PV\n氢中氧含量1(外部4线制有源4-20mA,AI及DC24V受DO通/断，配隔离器）':Cols.OTH_b,
        'AT_0204C.PV\n氧中氢含量2(外部4线制有源4-20mA,AI及DC24V受DO通/断，配隔离器）':Cols.HTO_b,
    }
)
df_all[Cols.HTO] = df_all[[Cols.HTO_a,Cols.HTO_b]].max(axis=1)
df_all[Cols.OTH] = df_all[[Cols.OTH_a,Cols.OTH_b]].max(axis=1)

for col in df_all.columns[1:]:
    df_all[col] = df_all[col].apply(
        lambda x: np.nan if x == '--' else x
    )
    df_all[col] = df_all[col].apply(float)
df_all[Cols.date_time] = df_all[Cols.date_time].apply(pd.to_datetime)
df_all= df_all.ffill()

df_all.columns

for col in df_all.columns:
    if '槽温' in col:
        print(col)


df_all[Cols.lye_temp_no.format(1)],df_all[Cols.lye_temp_no.format(2)],df_all[Cols.lye_temp_no.format(3)],df_all[Cols.lye_temp_no.format(4)] = [df_all['TT_0201C.PV\n碱液温(外部2线制无源4-20ma，配隔离器）'].copy()]*4

rename_dict = {}
rename_dict.update(
    {
        'FT_0302C.PV\n产品气流量':Cols.hydrogen_flow
    }
)
rename_dict.update(
    {
        f'FIC_020{No+2}C.PV\n碱液调节阀{No}'.format(No=No):Cols.lye_flow_no.format(No) for No in range(1,5)
    }
) # 碱液流量 不是严格对应的序号1:1,2:4,3:2,4:3（流量阀：电解槽）
rename_dict.update(
    {
        'II_ZLG_X010{No}C.PV\n整流柜X010{No}C电流反馈'.format(No=No):Cols.current_no.format(No) for No in range(1,5)
    }
) # 电流
rename_dict.update(
    {
        'EI_ZLG_X010{No}C.PV\n整流柜X010{No}C电压反馈'.format(No=No):Cols.voltage_no.format(No) for No in range(1,5)
    }
) # 电压
rename_dict.update(
    {
        'PW_ZLG_X010{No}C.VALUE\nX010{No}C直流电耗'.format(No=No):Cols.DC_energy_cost_no.format(No) for No in range(1,5)
    }
) # 直流电耗
rename_dict.update(
    {
        f'TT_010{No}C.PV\n{No}#电解槽氧槽温1(外部2线制无源4-20ma，配隔离器）'.format(No=No):Cols.temp_O_no.format(No) for No in range(1,5)
    }
) # 氧槽温
rename_dict.update(
    {
        f'TT_010{No+4}C.PV\n{No}#电解槽氢槽温(外部2线制无源4-20ma，配隔离器）'.format(No=No):Cols.temp_H_no.format(No) for No in range(1,5)
    }
) # 氢槽温

for col in rename_dict.keys():
    if col not in df_all.columns:
        print(col)

df_all = df_all.rename(
    columns=rename_dict
)
def column_length(column_name):
    # 检测如果包含中文字符，就给出一个较长的长度
    if re.search(r'[\u4e00-\u9fff]', column_name):
        return 550
    elif column_name == Cols.date_time:
        return 1
    else:
        return len(column_name)
columns = df_all.columns
columns = sorted(columns, key = lambda x: column_length(x))
df_all = df_all[columns]

for idx in range(1,5):
    df_all[Cols.temp_out_no.format(idx)] = (df_all[Cols.temp_H_no.format(idx)] + df_all[Cols.temp_O_no.format(idx)])/2
    df_all[Cols.cell_voltage_no.format(idx)] = df_all[Cols.voltage_no.format(idx)] / 238
    df_all[Cols.current_density_no.format(idx)] = df_all[Cols.current_no.format(idx)] / (np.pi*0.916**2)

# 预存储合并后结果
with gzip.open(
    os.path.join(
        target_folder,
        'Hi1_大唐_20250115 gzip'
    ),
    'wb'

) as f:
    pickle.dump(df_all,f)

df_all.to_excel(
    os.path.join(
        target_folder,
        'Hi1_大唐_20250115.xlsx'
    )
)



folder = '阳光运行数据'
cur_folder = os.path.join(source_folder,folder)
print(folder)
file_list = os.listdir(cur_folder)

col_set = set()
df_list = []
for file in file_list:
    if '.xlsx' in file:
        df_cur = pd.read_excel(
            os.path.join(
                cur_folder,
                file
            )
        )
        for col in df_cur.columns:
            col_set.add(col)
        
        df_list.append(df_cur)

df_res=df_list[0].drop(columns = ['序号\n'])
for df in df_list[1:]:
    df_res = pd.merge(
        left = df_res,
        right = df.drop(columns =[ '序号\n']),
        how = 'outer',
        on = '时间\n'
    )

# 预存储合并后结果
with gzip.open(
    os.path.join(
        cur_folder,
        '阳光 all records gzip'
    ),
    'wb'

) as f:
    pickle.dump(df_res,f)

df_res.columns

df_all = df_res.rename(
    columns = {
        '时间\n':Cols.date_time,
        'AT_0201B.PV\n氢中氧含量1':Cols.OTH_a,
        'AT_0202B.PV\n氧中氢含量1':Cols.HTO_a,
        'AT_0203B.PV\n氢中氧含量2':Cols.OTH_b,
        'AT_0204B.PV\n氧中氢含量2':Cols.HTO_b,
    }
)
df_all[Cols.HTO] = df_all[[Cols.HTO_a,Cols.HTO_b]].max(axis=1)
df_all[Cols.OTH] = df_all[[Cols.OTH_a,Cols.OTH_b]].max(axis=1)

for col in df_all.columns[1:]:
    df_all[col] = df_all[col].apply(
        lambda x: np.nan if x == '--' else x
    )
    df_all[col] = df_all[col].apply(float)
df_all[Cols.date_time] = df_all[Cols.date_time].apply(pd.to_datetime)
df_all= df_all.ffill()





import pandas as pd


def remove_duplicate_columns(df):
    columns = []
    unique_columns = []
    for col in df.columns:
        if col.endswith('_x') or col.endswith('_y'):
            base_col = col[:-2]
            if base_col in unique_columns:
                continue
            df = df.rename(
                columns = {
                    col:base_col
                }
            )
            columns.append(base_col)
            unique_columns.append(base_col)
        else:
            columns.append(col)
            unique_columns.append(col)
    return df[columns]

df_all = remove_duplicate_columns(df_all)


df_all.columns

for col in df_all.columns:
    if '电压' in col:
        print(col)


rename_dict = {}
rename_dict.update(
    {
         'FT_0205B.PV\n粗氢流量':Cols.hydrogen_flow
    }
)
rename_dict.update(
    {
        'FT_020{No}B.PV\n电解槽X010{No}C碱液流量'.format(No=No):Cols.lye_flow_no.format(No) for No in range(1,5)
    }
) # 碱液流量
rename_dict.update(
    {
        'II_ZLG_X010{No}B.PV\n整流柜X010{No}B电流反馈'.format(No=No):Cols.current_no.format(No) for No in range(1,5)
    }
) # 电流
rename_dict.update(
    {
        'PW_ZLG_X010{No}B.VALUE\nX010{No}B电解槽直流电耗'.format(No=No):Cols.DC_energy_cost_no.format(No) for No in range(1,5)
    }
) # 直流电耗
rename_dict.update(
    {
        f'TT_010{No*2-1}B.PV\n电解槽X010{No}B氢侧出口温度'.format(No=No):Cols.temp_H_no.format(No) for No in range(1,5)
    }
) # 氢侧温度
rename_dict.update(
    {
        f'TT_010{No*2}B.PV\n电解槽X010{No}B氧侧出口温度'.format(No=No):Cols.temp_O_no.format(No) for No in range(1,5)
    }
) # 氢侧温度

for col in rename_dict.keys():
    if col not in df_all.columns:
        print(col)

df_all = df_all.rename(
    columns=rename_dict
)
def column_length(column_name):
    # 检测如果包含中文字符，就给出一个较长的长度
    if re.search(r'[\u4e00-\u9fff]', column_name):
        return 550
    elif column_name == Cols.date_time:
        return 1
    else:
        return len(column_name)
columns = df_all.columns
columns = sorted(columns, key = lambda x: column_length(x))
df_all = df_all[columns]

for idx in range(1,5):
    df_all[Cols.temp_out_no.format(idx)] = (df_all[Cols.temp_H_no.format(idx)] + df_all[Cols.temp_O_no.format(idx)])/2
    df_all[Cols.cell_voltage_no.format(idx)] = df_all[Cols.DC_energy_cost_no.format(idx)] / 2.39
    df_all[Cols.current_density_no.format(idx)] = df_all[Cols.current_no.format(idx)] / (np.pi*0.89**2)

plt.hist(df_all[Cols.cell_voltage_no.format(1)])

# 预存储合并后结果
with gzip.open(
    os.path.join(
        target_folder,
        '718_阳光_20250115 gzip'
    ),
    'wb'

) as f:
    pickle.dump(df_all,f)

df_all.to_excel(
    os.path.join(
        target_folder,
        '718_阳光_20250115.xlsx'
    )
)

folder = '718运行数据'
cur_folder = os.path.join(source_folder,folder)
print(folder)
file_list = os.listdir(cur_folder)

col_set = set()
df_list = []
for file in file_list:
    if '.xlsx' in file:
        df_cur = pd.read_excel(
            os.path.join(
                cur_folder,
                file
            )
        )
        for col in df_cur.columns:
            col_set.add(col)
        
        df_list.append(df_cur)

df_res=df_list[0].drop(columns = ['序号\n'])
for df in df_list[1:]:
    df_res = pd.merge(
        left = df_res,
        right = df.drop(columns =[ '序号\n']),
        how = 'outer',
        on = '时间\n'
    )

# 预存储合并后结果
with gzip.open(
    os.path.join(
        cur_folder,
        '718 all records gzip'
    ),
    'wb'

) as f:
    pickle.dump(df_res,f)

df_all = df_res.rename(
    columns = {
        '时间\n':Cols.date_time,
        'AT0201A.PV\n氢中氧含量检测、报警':Cols.OTH_a,
        'AT0202A.PV\n氧中氢含量检测、报警':Cols.HTO_a,
        'AT0203A.PV\n氢中氧含量检测、报警':Cols.OTH_b,
        'AT0204A.PV\n氧中氢含量检测、报警':Cols.HTO_b,
    }
)
df_all[Cols.HTO] = df_all[[Cols.HTO_a,Cols.HTO_b]].max(axis=1)
df_all[Cols.OTH] = df_all[[Cols.OTH_a,Cols.OTH_b]].max(axis=1)
df_all[Cols.date_time] = df_all[Cols.date_time].apply(pd.to_datetime)


for col in df_all.columns[1:]:
    df_all[col] = df_all[col].apply(
        lambda x: np.nan if x == '--' else x
    )
    df_all[col] = df_all[col].apply(float)
df_all= df_all.ffill()



df_all.columns

plt.hist(df_all['FIT0205A.PV\n氢气流量检测_y'])

for col in df_all.columns:
    if '氢气' in col:
        print(col)


df_all[Cols.lye_temp_no.format(1)],df_all[Cols.lye_temp_no.format(2)] = [df_all[['TT0201A.PV\n1#和2#电解槽碱液温度检测、报警']].copy()]*2
df_all[Cols.lye_temp_no.format(3)],df_all[Cols.lye_temp_no.format(4)] = [df_all[['TT0203A.PV\n3#和4#电解槽碱液温度检测、报警']].copy()]*2
df_all =  df_all.drop(columns=[
    'TT0201A.PV\n1#和2#电解槽碱液温度检测、报警',
    'TT0203A.PV\n3#和4#电解槽碱液温度检测、报警'
])

rename_dict = {}
rename_dict.update(
    {
         'FIT0205A.PV\n氢气流量检测_x':Cols.hydrogen_flow
    }
)
rename_dict.update(
    {
        'EI_ZLG_X010{No}C.PV\n整流柜X010{No}C电压反馈'.format(No=No):Cols.voltage_no.format(No) for No in range(1,5)
    }
) # 电压
rename_dict.update(
    {
        'PW_ZLG_X010{No}A.VALUE\nX010{No}A电解槽直流电耗'.format(No=No):Cols.DC_energy_cost_no.format(No) for No in range(1,5)
    }
) # 直流电耗
rename_dict.update(
    {
        'II_ZLG_X010{No}A.PV\n整流柜X010{No}A电流反馈'.format(No=No):Cols.current_no.format(No) for No in range(1,5)
    }
) # 直流电流
rename_dict.update(
    {
        f'TT010{No*2-1}A.PV\n{No}#电解槽氢侧出口温度检测、报警连锁'.format(No=No):Cols.temp_H_no.format(No) for No in range(1,5)
    }
) # 氢槽温
rename_dict.update(
    {
        f'TT010{No*2}A.PV\n{No}#电解槽氧侧出口温度检测、报警连锁'.format(No=No):Cols.temp_O_no.format(No) for No in range(1,5)
    }
) # 氢槽温

for col in rename_dict.keys():
    if col not in df_all.columns:
        print(col)

df_all = df_all.rename(
    columns=rename_dict
)

def column_length(column_name):
    # 检测如果包含中文字符，就给出一个较长的长度
    if re.search(r'[\u4e00-\u9fff]', column_name):
        return 550
    elif column_name == Cols.date_time:
        return 1
    else:
        return len(column_name)
columns = df_all.columns
columns = sorted(columns, key = lambda x: column_length(x))
df_all = df_all[columns]

for idx in range(1,5):
    df_all[Cols.temp_out_no.format(idx)] = (df_all[Cols.temp_H_no.format(idx)] + df_all[Cols.temp_O_no.format(idx)])/2
    df_all[Cols.cell_voltage_no.format(idx)] = df_all[Cols.DC_energy_cost_no.format(idx)] / 2.39
    df_all[Cols.current_density_no.format(idx)] = df_all[Cols.current_no.format(idx)] / (np.pi*0.91**2)

plt.hist(df_all[Cols.current_density_no.format(1)])

# 预存储合并后结果
with gzip.open(
    os.path.join(
        target_folder,
        '718_大唐_20250115 gzip'
    ),
    'wb'

) as f:
    pickle.dump(df_all,f)

df_all.to_excel(
    os.path.join(
        target_folder,
        '718_大唐_20250115.xlsx'
    )
)





