#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
综合分析框架 - 整合红外、性能、极化分析
"""

import pandas as pd
import numpy as np
from keys import Cols
from performance_analysis.fit import performance_extraction
from polarization.polar_function import polar_temp_dist
import matplotlib.pyplot as plt
import seaborn as sns

class ComprehensiveAnalyzer:
    """综合分析器 - 整合多维度分析"""
    
    def __init__(self, project_name):
        self.project_name = project_name
        self.thermal_data = {}
        self.performance_data = {}
        self.polarization_data = {}
        
    def load_thermal_analysis(self, thermal_results):
        """加载红外温度分析结果"""
        self.thermal_data = {
            'temperature_distribution': thermal_results.get('temperatures', []),
            'hot_spots': self._identify_hot_spots(thermal_results),
            'temperature_uniformity': self._calculate_uniformity(thermal_results),
            'thermal_efficiency': self._estimate_thermal_efficiency(thermal_results)
        }
    
    def load_performance_data(self, df_performance):
        """加载性能分析数据"""
        # 提取关键性能指标
        R_slope, V_inter, HTO_a, HTO_b, OTH_a, OTH_b = performance_extraction(df_performance)
        
        self.performance_data = {
            'resistance_slope': R_slope,  # 电阻斜率
            'voltage_intercept': V_inter,  # 电压截距
            'HTO_parameters': (HTO_a, HTO_b),  # 氧中氢参数
            'OTH_parameters': (OTH_a, OTH_b),  # 氢中氧参数
            'current_efficiency': self._calculate_current_efficiency(df_performance),
            'energy_consumption': self._calculate_energy_consumption(df_performance)
        }
    
    def correlate_thermal_performance(self):
        """关联温度与性能分析"""
        correlations = {}
        
        if self.thermal_data and self.performance_data:
            # 温度均匀性与电流效率的关系
            correlations['temp_uniformity_vs_efficiency'] = self._correlate_uniformity_efficiency()
            
            # 热点与电压损失的关系
            correlations['hot_spots_vs_voltage_loss'] = self._correlate_hotspots_voltage()
            
            # 温度分布与纯度的关系
            correlations['temp_distribution_vs_purity'] = self._correlate_temp_purity()
            
        return correlations

    def _correlate_uniformity_efficiency(self):
        """关联温度均匀性与电流效率"""
        if not self.thermal_data or not self.performance_data:
            return 0

        uniformity = self.thermal_data.get('temperature_uniformity', 0)
        efficiency = self.performance_data.get('current_efficiency', 0)

        # 简化的相关性计算
        return uniformity * efficiency

    def _correlate_hotspots_voltage(self):
        """关联热点与电压损失"""
        if not self.thermal_data or not self.performance_data:
            return 0

        hot_spots = self.thermal_data.get('hot_spots', {}).get('count', 0)
        voltage = self.performance_data.get('voltage_intercept', 0)

        # 简化的相关性计算
        return hot_spots * voltage if voltage > 0 else 0

    def _correlate_temp_purity(self):
        """关联温度分布与纯度"""
        if not self.thermal_data or not self.performance_data:
            return 0

        uniformity = self.thermal_data.get('temperature_uniformity', 0)
        hto_params = self.performance_data.get('HTO_parameters', (0, 0))

        # 简化的相关性计算
        return uniformity * (1 / (1 + hto_params[0])) if hto_params[0] > 0 else 0
    
    def generate_optimization_recommendations(self):
        """生成优化建议"""
        recommendations = []
        
        # 基于温度分析的建议
        if self.thermal_data.get('temperature_uniformity', 1.0) < 0.8:
            recommendations.append({
                'category': '温度控制',
                'priority': 'HIGH',
                'issue': '温度分布不均匀',
                'recommendation': '检查冷却系统，优化流场设计',
                'expected_improvement': '提高电流效率2-5%'
            })
        
        # 基于性能分析的建议
        if self.performance_data.get('current_efficiency', 0) < 0.85:
            recommendations.append({
                'category': '电解效率',
                'priority': 'HIGH',
                'issue': '电流效率偏低',
                'recommendation': '优化电流密度分布，检查电极状态',
                'expected_improvement': '降低能耗5-10%'
            })
        
        return recommendations
    
    def _identify_hot_spots(self, thermal_results):
        """识别热点"""
        temperatures = thermal_results.get('temperatures', [])
        if len(temperatures) == 0:
            return []
        
        temp_array = np.array(temperatures)
        threshold = np.percentile(temp_array, 95)  # 95%分位数作为热点阈值
        hot_spots = temp_array[temp_array > threshold]
        
        return {
            'count': len(hot_spots),
            'max_temp': np.max(hot_spots) if len(hot_spots) > 0 else 0,
            'avg_temp': np.mean(hot_spots) if len(hot_spots) > 0 else 0,
            'threshold': threshold
        }
    
    def _calculate_uniformity(self, thermal_results):
        """计算温度均匀性指数"""
        temperatures = thermal_results.get('temperatures', [])
        if len(temperatures) == 0:
            return 0
        
        temp_array = np.array(temperatures)
        cv = np.std(temp_array) / np.mean(temp_array)  # 变异系数
        uniformity_index = 1 / (1 + cv)  # 均匀性指数 (0-1)
        
        return uniformity_index
    
    def _estimate_thermal_efficiency(self, thermal_results):
        """估算热效率"""
        # 基于温度分布估算热效率
        temperatures = thermal_results.get('temperatures', [])
        if len(temperatures) == 0:
            return 0
        
        temp_array = np.array(temperatures)
        # 简化的热效率估算模型
        optimal_temp = 80  # 假设最优工作温度
        temp_deviation = np.mean(np.abs(temp_array - optimal_temp))
        thermal_efficiency = max(0, 1 - temp_deviation / optimal_temp)
        
        return thermal_efficiency
    
    def _calculate_current_efficiency(self, df):
        """计算电流效率"""
        if Cols.current_efficiency in df.columns:
            return df[Cols.current_efficiency].mean()
        return 0
    
    def _calculate_energy_consumption(self, df):
        """计算能耗"""
        if Cols.energy_cost in df.columns:
            return df[Cols.energy_cost].mean()
        return 0
    
    def generate_comprehensive_report(self):
        """生成综合分析报告"""
        report = {
            'project': self.project_name,
            'thermal_analysis': self.thermal_data,
            'performance_analysis': self.performance_data,
            'correlations': self.correlate_thermal_performance(),
            'recommendations': self.generate_optimization_recommendations(),
            'overall_score': self._calculate_overall_score()
        }
        
        return report
    
    def _calculate_overall_score(self):
        """计算综合评分"""
        scores = []
        
        # 温度评分
        if self.thermal_data:
            temp_score = self.thermal_data.get('temperature_uniformity', 0) * 100
            scores.append(temp_score)
        
        # 性能评分
        if self.performance_data:
            eff_score = self.performance_data.get('current_efficiency', 0) * 100
            scores.append(eff_score)
        
        return np.mean(scores) if scores else 0
