#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Run the Infrared Analysis application
"""

import sys
import os

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

print("Starting Infrared Analysis Application...")
print("Python version:", sys.version)

try:
    # Import the app
    from Infrared_analysis.app import app
    print("App imported successfully!")
    
    print("Starting server on http://127.0.0.1:8050/")
    print("Press Ctrl+C to stop the server")
    
    # Start the server
    app.run_server(debug=True, host='127.0.0.1', port=8050)
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
