#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Debug version of the Infrared Analysis application
"""

import sys
import os

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

print("="*60)
print("INFRARED ANALYSIS APPLICATION - DEBUG MODE")
print("="*60)

print(f"Python version: {sys.version}")
print(f"Current working directory: {os.getcwd()}")
print(f"Script location: {os.path.abspath(__file__)}")

# Check data directory
data_dir = os.path.join(os.path.dirname(__file__), 'data', 'raw', 'Infrared')
print(f"Data directory: {data_dir}")
print(f"Data directory exists: {os.path.exists(data_dir)}")

if os.path.exists(data_dir):
    try:
        folders = [f for f in os.listdir(data_dir) if os.path.isdir(os.path.join(data_dir, f))]
        print(f"Found {len(folders)} data folders")
        if folders:
            print(f"Sample folders: {folders[:3]}")
    except Exception as e:
        print(f"Error reading data directory: {e}")

print("\n" + "-"*40)
print("Testing imports...")

try:
    import dash
    print(f"✓ Dash version: {dash.__version__}")
    
    import plotly
    print(f"✓ Plotly version: {plotly.__version__}")
    
    import pandas as pd
    print(f"✓ Pandas version: {pd.__version__}")
    
    import numpy as np
    print(f"✓ NumPy version: {np.__version__}")
    
    from Infrared_analysis.utils_image import read_infrared_image
    print("✓ Utils imported successfully")
    
    print("\n" + "-"*40)
    print("Importing main app...")
    
    from Infrared_analysis.app import app, ROOT_DIR
    print(f"✓ App imported successfully")
    print(f"✓ App ROOT_DIR: {ROOT_DIR}")
    print(f"✓ ROOT_DIR exists: {os.path.exists(ROOT_DIR)}")
    
    print("\n" + "="*60)
    print("STARTING SERVER...")
    print("="*60)
    print("Server URL: http://127.0.0.1:8050/")
    print("Press Ctrl+C to stop the server")
    print("="*60)
    
    # Start the server with more verbose output
    app.run(
        debug=True,
        host='127.0.0.1',
        port=8050,
        dev_tools_hot_reload=True,
        dev_tools_ui=True
    )
    
except ImportError as e:
    print(f"✗ Import error: {e}")
    import traceback
    traceback.print_exc()
    input("Press Enter to exit...")
    sys.exit(1)
except Exception as e:
    print(f"✗ Error: {e}")
    import traceback
    traceback.print_exc()
    input("Press Enter to exit...")
    sys.exit(1)
