import numpy as np

from statsmodels.tools.tools import Bunch

cov_clu_stata = np.array([
    .00025262993207,
    -.00065043385106,
    .20961897960949,
    -.00065043385106,
    .00721940994738,
    -1.2171040967615,
    .20961897960949,
    -1.2171040967615,
    417.18890043724]).reshape(3, 3)

cov_pnw0_stata = np.array([
    .00004638910396,
    -.00006781406833,
    -.00501232990882,
    -.00006781406833,
    .00238784043122,
    -.49683062350622,
    -.00501232990882,
    -.49683062350622,
    133.97367476797]).reshape(3, 3)

cov_pnw1_stata = np.array([
    .00007381482253,
    -.00009936717692,
    -.00613513582975,
    -.00009936717692,
    .00341979122583,
    -.70768252183061,
    -.00613513582975,
    -.70768252183061,
    197.31345000598]).reshape(3, 3)

cov_pnw4_stata = np.array([
    .0001305958131,
    -.00022910455176,
    .00889686530849,
    -.00022910455176,
    .00468152667913,
    -.88403667445531,
    .00889686530849,
    -.88403667445531,
    261.76140136858]).reshape(3, 3)

cov_dk0_stata = np.array([
    .00005883478135,
    -.00011241470772,
    -.01670183921469,
    -.00011241470772,
    .00140649264687,
    -.29263014921586,
    -.01670183921469,
    -.29263014921586,
    99.248049966902]).reshape(3, 3)

cov_dk1_stata = np.array([
    .00009855800275,
    -.00018443722054,
    -.03257408922788,
    -.00018443722054,
    .00205106413403,
    -.3943459697384,
    -.03257408922788,
    -.3943459697384,
    140.50692606398]).reshape(3, 3)

cov_dk4_stata = np.array([
    .00018052657317,
    -.00035661054613,
    -.06728261073866,
    -.00035661054613,
    .0024312795189,
    -.32394785247278,
    -.06728261073866,
    -.32394785247278,
    148.60456447156]).reshape(3, 3)


results = Bunch(
    cov_clu_stata=cov_clu_stata,
    cov_pnw0_stata=cov_pnw0_stata,
    cov_pnw1_stata=cov_pnw1_stata,
    cov_pnw4_stata=cov_pnw4_stata,
    cov_dk0_stata=cov_dk0_stata,
    cov_dk1_stata=cov_dk1_stata,
    cov_dk4_stata=cov_dk4_stata
)
