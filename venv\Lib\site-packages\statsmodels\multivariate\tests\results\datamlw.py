from numpy import array

from statsmodels.tools.testing import Holder


data = Holder()
data.comment = 'generated data, divide by 1000'
data.name = 'data'
data.xo = array([
    [-419, -731, -1306, -1294],
    [6, 529, -200, -437],
    [-27, -833, -6, -564],
    [-304, -273, -502, -739],
    [1377, -912, 927, 280],
    [-375, -517, -514, 49],
    [247, -504, 123, -259],
    [712, 534, -773, 286],
    [195, -1080, 3256, -178],
    [-854, 75, -706, -1084],
    [-1219, -612, -15, -203],
    [550, -628, -483, -2686],
    [-365, 1376, -1266, 317],
    [-489, 544, -195, 431],
    [-656, 854, 840, -723],
    [16, -1385, -880, -460],
    [258, -2252, 96, 54],
    [2049, -750, -1115, 381],
    [-65, 280, -777, 416],
    [755, 82, -806, 1027],
    [-39, -170, -2134, 743],
    [-859, 780, 746, -133],
    [762, 252, -450, -459],
    [-941, -202, 49, -202],
    [-54, 115, 455, 388],
    [-1348, 1246, 1430, -480],
    [229, -535, -1831, 1524],
    [-651, -167, 2116, 483],
    [-1249, -1373, 888, -1092],
    [-75, -2162, 486, -496],
    [2436, -1627, -1069, 162],
    [-63, 560, -601, 587],
    [-60, 1051, -277, 1323],
    [1329, -1294, 68, 5],
    [1532, -633, -923, 696],
    [669, 895, -1762, -375],
    [1129, -548, 2064, 609],
    [1320, 573, 2119, 270],
    [-213, -412, -2517, 1685],
    [73, -979, 1312, -1220],
    [-1360, -2107, -237, 1522],
    [-645, 205, -543, -169],
    [-212, 1072, 543, -128],
    [-352, -129, -605, -904],
    [511, 85, 167, -1914],
    [1515, 1862, 942, 1622],
    [-465, 623, -495, -89],
    [-1396, -979, 1758, 128],
    [-255, -47, 980, 501],
    [-1282, -58, -49, -610],
    [-889, -1177, -492, 494],
    [1415, 1146, 696, -722],
    [1237, -224, -1609, -64],
    [-528, -1625, 231, 883],
    [-327, 1636, -476, -361],
    [-781, 793, 1882, 234],
    [-506, -561, 1988, -810],
    [-1233, 1467, -261, 2164],
    [53, 1069, 824, 2123],
    [-1200, -441, -321, 339],
    [1606, 298, -995, 1292],
    [-1740, -672, -1628, -129],
    [-1450, -354, 224, -657],
    [-2556, 1006, -706, -1453],
    [-717, -463, 345, -1821],
    [1056, -38, -420, -455],
    [-523, 565, 425, 1138],
    [-1030, -187, 683, 78],
    [-214, -312, -1171, -528],
    [819, 736, -265, 423],
    [1339, 351, 1142, 579],
    [-387, -126, -1573, 2346],
    [969, 2, 327, -134],
    [163, 227, 90, 2021],
    [1022, -1076, 174, 304],
    [1042, 1317, 311, 880],
    [2018, -840, 295, 2651],
    [-277, 566, 1147, -189],
    [20, 467, 1262, 263],
    [-663, 1061, -1552, -1159],
    [1830, 391, 2534, -199],
    [-487, 752, -1061, 351],
    [-2138, -556, -367, -457],
    [-868, -411, -559, 726],
    [1770, 819, -892, -363],
    [553, -736, -169, -490],
    [388, -503, 809, -821],
    [-516, -1452, -192, 483],
    [493, 2904, 1318, 2591],
    [175, 584, -1001, 1675],
    [1316, -1596, -460, 1500],
    [1212, 214, -644, -696],
    [-501, 338, 1197, -841],
    [-587, -469, -1101, 24],
    [-1205, 1910, 659, 1232],
    [-150, 398, 594, 394],
    [34, -663, 235, -334],
    [-1580, 647, 239, -351],
    [-2177, -345, 1215, -1494],
    [1923, 329, -152, 1128]])

princomp1 = Holder()
princomp1.comment = 'mlab.princomp(x, nout=3)'
princomp1.factors = array([
    [-.83487832815382, -1.75681522344645, -.50882660928949, -.59661466511045],
    [-.18695786699253, -.10732909330422, .23971799542554, -.75468286946853],
    [-.57403949255604, -.39667006607544, -.7927838094217, .02652621881328],
    [-.60828125251513, -.75979035898754, -.20148864200404, -.40278856050237],
    [.55997928601548, .88869370546643, -1.55474410845786, .23033958281961],
    [-.18023239851961, -.72398923145328, -.07056264751117, .29292391015376],
    [-.189029743271, -.05888596186903, -.63882208368513, -.05682951829677],
    [.94694345324739, -.33448036234864, .16665867708366, -.67190948646953],
    [-1.355171899399, 2.58899695901774, -1.53157119606928, .93743278678908],
    [-1.06797676403358, -1.01894055566289, .29181722134698, -.65261957826524],
    [-1.08919199915725, -.5395876105009, .18846579824378, .61935728909742],
    [-1.36598849770841, -1.00986627679465, -1.6090477073157, -1.82708847399443],  # noqa:E501
    [.561511276285, -.74919011595195, 1.49872898209738, -.80588545345232],
    [.04805787176428, -.05522267212748, .82943784435024, .01537039050312],
    [-1.12006939155398, .73462770352006, .58868274831601, -.67786987413505],
    [-.26087838474316, -1.33362289066951, -1.02932517860259, .24865839951801],
    [-.24666198784909, -.58247196399204, -1.78971960966265, 1.18908143657302],
    [1.80675592845666, -.73341258204636, -1.45012544705912, -.44875329121288],
    [.4794281391435, -.57169295903913, .48557628591056, -.11638075289238],
    [1.39425263398653, -.3665732682294, .06937942447187, .06683559082703],
    [1.11015707065101, -1.87631329249852, .48914958604867, .11096926802212],
    [-.85159530389901, .68543874135386, .86736021483251, -.17641002537865],
    [.34109015314112, -.25431311542374, -.36804227540019, -.95824474920131],
    [-.86253950274987, -.28796613689709, .30820634958709, .27228599921917],
    [.01266190412089, .48559962017667, .14020630700546, .18517398749337],
    [-1.56345869427724, 1.27917754070516, 1.25640847929385, -.36055181722313],
    [1.62834293379132, -1.51923809467869, .27754976407182, .79362967384835],
    [-.94400458067084, 1.77733054371289, .03595731772774, .96570688640992],
    [-2.11906234438329, -.13226430948321, -.78992396115366, .66362103473975],
    [-.94372331181891, -.37502966791165, -1.77907324401749, .97801542954941],
    [1.76575198740032, -.92309597844861, -2.3872195277998, -.21817018301121],
    [.57418226616373, -.2925257318724, .71180507312941, -.13937750314467],
    [1.01654397566275, .28855305878842, 1.25119859389106, .11257524396004],
    [.58979013567212, -.06866577243092, -1.74447546690995, .13917953157575],
    [1.62072087150051, -.5835145063711, -.99029357957459, -.06334029436682],
    [.893493925425, -1.23995040005948, .40058503790479, -1.49029669097391],
    [.26990527585623, 2.03399854143898, -1.2335089890881, .54010061879979],
    [.33504096277444, 2.42394994177782, -.6643863358332, -.42471161848557],
    [1.69952476943058, -2.1707037237448, .79694026483866, .88177267205969],
    [-1.41498253257895, .65248089992094, -1.40045976465378, -.12045332880702],
    [-.22640706265253, -.94114558124915, -.18868114063537, 2.67652245892778],
    [-.37493712386529, -.61985213642068, .5383582946365, -.17931524703276],
    [-.30437796317839, .74252786648649, .73255373596822, -.64993745548429],
    [-.68788283675831, -.84714762684627, -.10721753874211, -.59777382822281],
    [-1.00667616522842, -.06670525233919, -.92973707141688, -1.60742284256649],
    [1.95220512266515, 2.05751265066695, .79640648143073, -.59608004229343],
    [-.15504464969388, -.3882079443045, .75049869361395, -.44163703260023],
    [-1.6686863460652, .96325894557423, -.16453379247258, 1.4560996746313],
    [-.25573631707529, .88265554068571, .08984550855664, .53561910563178],
    [-1.29430028690793, -.48042359291447, .49318558750269, .03689178852848],
    [-.34391235307349, -.95154811896716, -.09714022474353, 1.19792361047367],
    [.34367523316975, 1.16641214447854, -.39528838072965, -1.72565643987406],
    [1.23887392116229, -1.27474554996132, -.65859544264097, -.81757560038832],
    [-.17739006831099, -.29057501559843, -.62533324788504, 1.7092669546224],
    [-.08610919021307, -.06524996994257, 1.3018284944661, -1.28219607271255],
    [-.95717735853496, 1.79841555744597, .75799149339397, .23542916575208],
    [-1.70175078442029, 1.33831900642462, -.73979048943944, .26157699746442],
    [.84631686421106, .32029666775009, 2.51638540556813, .90367536744335],
    [1.22693220256582, 1.45665385966518, 1.27480662666555, .78786331120259],
    [-.59251239046609, -.660398245535, .53258334042042, .81248748854679],
    [2.22723057510913, -.22856960444805, -.15586801032885, -.26957090658609],
    [-.83192612439183, -2.11983096548132, .75319973501664, .62196293266702],
    [-1.577627210601, -.3747136286972, .31736538266249, .30187577548949],
    [-2.28230005998543, -1.17283119424281, 1.83780755209602, -.75928026219594],
    [-1.90574204329052, -.34197417196464, -.59978910354131, -.68240235236779],
    [.48132729275936, -.2524965456322, -.75271273075, -.89651237903089],
    [.26961427953002, .62968227134995, .99324664633985, .59917742452108],
    [-.95910506784013, .31907970712369, .35568397653203, .60155535679072],
    [-.18528259973205, -1.31831013869974, -.09749195643548, -.39885348684496],
    [.9608404103702, .23727553971573, .20695289013955, -.65281918968052],
    [.85302395609555, 1.5303724004181, -.56440186223081, -.27348033453255],
    [1.72786301913767, -1.14859994931789, 1.16222121440674, 1.39284961909257],
    [.37711527308989, .47231886947072, -.69423676772182, -.53515102147655],
    [1.35642227654922, .53204130038923, .69844068787197, 1.04544871561741],
    [.57797880484094, .08044525072063, -1.32634695941334, .35179408060132],
    [1.29437232500619, 1.07461562326311, .54545226737269, -.6836610122092],
    [2.74736726573105, .90881277479338, -.98342785084735, 1.38171127911719],
    [-.67749479829901, 1.10093727650063, .28416704607992, -.24984509303044],
    [-.24513961858774, 1.32098977907584, .16904762754153, .00886790270539],
    [-.5392290825383, -1.43851802284774, 1.0064737206577, -1.52649870396689],
    [.19486366400459, 2.77236000318994, -1.32201258472682, -.75922390642504],
    [.33271229220962, -.78464273816827, 1.09930224781861, -.32184679755027],
    [-1.72814706427698, -1.09275114767838, .7451569579997, .72871211772761],
    [-.035506207751, -.72161367235521, .52828318684787, .87177739169758],
    [1.31224955134141, -.22742530984642, -.44682270809773, -1.72769462581607],
    [-.07125058353119, -.36850925227739, -1.01188688859296, -.24962251325969],
    [-.69840680770104, .4925285516285, -1.0255829922787, -.36214090052941],
    [-.2530614593082, -.68595709316063, -.56882710610856, 1.25787365685572],
    [1.93782484285419, 2.67095706598253, 2.4023579082791, -.09112046819432],
    [1.57782156817208, -.39819017512275, 1.01938038947667, .39718992194809],
    [1.6839282738726, -.37808442385434, -1.36566197748227, 1.22029200163339],
    [.54652714502605, -.38206797548206, -.70554510441189, -1.31224358889695],
    [-1.30026063006148, .90642495630747, .02711437433058, -.44482098905042],
    [-.1239033493518, -1.29112252171673, .18092802221218, .22673242779457],
    [.01152882540055, 1.13242883415094, 2.34980443084773, .17712319903618],
    [-.0505195424414, .6807219067402, .37771832345982, .0842510459176],
    [-.44230076745505, -.07002728477811, -.6716520563439, .09637247949641],
    [-1.31245480585229, -.01674966464909, 1.21063252882651, -.03927111631335],
    [-2.94268586886381, .20925236551048, .30321714445262, .22027672852006],
    [2.04121905977187, .58496246543101, -.5192457175416, -.37212298770116]])
princomp1.values = array([
    [1.29489288337888],
    [1.12722515391348],
    [.94682423958163],
    [.65890241090379]])
princomp1.name = 'princomp1'
princomp1.coef = array([
    [.65989917631713, .22621848650964, -.5882833472413, -.40899997165748],
    [.15824945056105, .3189419948895, .71689623797385, -.5994104597619],
    [-.3488766362785, .90294049788532, -.17151017930575, .1832151967827],
    [.64635538301471, .17832458477678, .33251578268108, .66321815082225]])

princomp2 = Holder()
princomp2.comment = 'mlab.princomp(x[:20,], nout=3)'
princomp2.factors = array([
    [.74592631465403, -.92093638563647, 1.10020213969681, -.20234362115983],
    [.40379773814409, -.23694214086306, -.53526599590626, .48048423978257],
    [-.43826559396565, -.26267383420164, .35939862515391, -.15176605914773],
    [.29427656853499, -.56363285386285, .19525662206552, -.0384830001072],
    [-1.4327917748351, 1.18414191887856, .05435949672922, .46861687286613],
    [.23033214569426, -.00452237842477, .00346120473054, -.61483888402985],
    [-.40976419499281, .10137131352284, .02570805136468, .06798926306103],
    [.83201287149759, .82736894861103, -.35298970920805, .49344802383821],
    [-3.36634598435507, -.18324521714611, -1.12118215528184, .2057949493723],
    [.70198992281665, -1.1856449495675, .02465727900177, -.08333428418838],
    [-.13789069679894, -.79430992968357, -.33106496391047, -1.01808298459082],
    [-.10779840884825, -1.41970796854378, 1.55590290358904, 1.34014813517248],
    [1.8229340670437, .13065838030104, -1.06152350166072, .11456488463131],
    [.51650051521229, .07999783864926, -1.08601194413786, -.28255247881905],
    [-.24654203558433, -1.02895891025197, -1.34475655787845, .52240852619949],
    [.03542169335227, -.01198903021187, 1.12649412049726, -.60518306798831],
    [-1.23945075955452, .48778599927278, 1.11522465483282, -.994827967694],
    [.30661562766349, 1.91993049714024, 1.08834307939522, .61608892787963],
    [.8241280516035, .43533554216801, -.48261931874702, -.22391158066897],
    [.6649139327178, 1.44597315984982, -.33359403032613, -.094219894409]])
princomp2.values = array([
    [1.16965204468073],
    [.77687367815155],
    [.72297937656591],
    [.32548581375971]])
princomp2.name = 'princomp2'
princomp2.coef = array([
    [-.13957162231397, .6561182967648, .32256106777669, .66781951188167],
    [.49534264552989, -.08241251099014, -.6919444767593, .51870674049413],
    [-.85614372781797, -.11427402995055, -.47665923729502, .16357058078438],
    [.04661912785591, .74138950947638, -.43584764555793, -.50813884128056]])

princomp3 = Holder()
princomp3.comment = 'mlab.princomp(x[:20,]-x[:20,].mean(0), nout=3)'
princomp3.factors = array([
    [.74592631465403, -.92093638563647, 1.10020213969681, -.20234362115983],
    [.40379773814409, -.23694214086306, -.53526599590626, .48048423978257],
    [-.43826559396565, -.26267383420164, .35939862515391, -.15176605914773],
    [.29427656853499, -.56363285386285, .19525662206552, -.0384830001072],
    [-1.4327917748351, 1.18414191887856, .05435949672922, .46861687286613],
    [.23033214569426, -.00452237842477, .00346120473054, -.61483888402985],
    [-.40976419499281, .10137131352284, .02570805136468, .06798926306103],
    [.83201287149759, .82736894861103, -.35298970920805, .49344802383821],
    [-3.36634598435507, -.18324521714611, -1.12118215528184, .2057949493723],
    [.70198992281665, -1.1856449495675, .02465727900177, -.08333428418838],
    [-.13789069679894, -.79430992968357, -.33106496391047, -1.01808298459082],
    [-.10779840884825, -1.41970796854378, 1.55590290358904, 1.34014813517248],
    [1.8229340670437, .13065838030104, -1.06152350166072, .11456488463131],
    [.51650051521229, .07999783864926, -1.08601194413786, -.28255247881905],
    [-.24654203558433, -1.02895891025197, -1.34475655787845, .52240852619949],
    [.03542169335227, -.01198903021187, 1.12649412049726, -.60518306798831],
    [-1.23945075955452, .48778599927278, 1.11522465483282, -.994827967694],
    [.30661562766349, 1.91993049714024, 1.08834307939522, .61608892787963],
    [.8241280516035, .43533554216801, -.48261931874702, -.22391158066897],
    [.6649139327178, 1.44597315984982, -.33359403032613, -.094219894409]])
princomp3.values = array([
    [1.16965204468073],
    [.77687367815155],
    [.72297937656591],
    [.32548581375971]])
princomp3.name = 'princomp3'
princomp3.coef = array([
    [-.13957162231397, .6561182967648, .32256106777669, .66781951188167],
    [.49534264552989, -.08241251099014, -.6919444767593, .51870674049413],
    [-.85614372781797, -.11427402995055, -.47665923729502, .16357058078438],
    [.04661912785591, .74138950947638, -.43584764555793, -.50813884128056]])
