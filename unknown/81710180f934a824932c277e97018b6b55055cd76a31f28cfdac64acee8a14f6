import numpy as np
import os
import pandas as pd
from statsmodels.tools.testing import Holder

cur_dir = os.path.dirname(os.path.abspath(__file__))

results_meth = Holder()
results_meth.type = 'ML'
results_meth.method = 'BFGS'
results_meth.scoring = 3
results_meth.start = np.array([
    1.44771372395646, 0.0615237727637243, 0.604926837329731, 0.98389051740736,
    6.25859738441389, 0
    ])
results_meth.n = 36
results_meth.nobs = 36
results_meth.df_null = 34
results_meth.df_residual = 30
results_meth.loglik = 104.148028405343
results_meth.vcov = np.array([
    0.00115682165449043, -0.000665413980696048, -0.000924081767589657,
    -0.000924126199147583, 0.000941505276523348, -1.44829373972985e-05,
    -0.000665413980696048, 0.00190019966824938, 4.45163588328844e-06,
    6.23668249663711e-06, -0.00216418558500309, 4.18754929463506e-05,
    -0.000924081767589657, 4.45163588328844e-06, 0.0023369966334575,
    0.000924223263225116, 0.000168988804218447, 1.14762434349836e-07,
    -0.000924126199147583, 6.23668249663711e-06, 0.000924223263225116,
    0.00282071714820361, 0.000331499252772628, 1.93773358431975e-07,
    0.000941505276523348, -0.00216418558500309, 0.000168988804218447,
    0.000331499252772628, 3.20761137509433, -0.0581708456538647,
    -1.44829373972985e-05, 4.18754929463506e-05, 1.14762434349836e-07,
    1.93773358431975e-07, -0.0581708456538647, 0.00107353277853341
    ]).reshape(6, 6, order='F')
results_meth.pseudo_r_squared = 0.905194911478503
results_meth.y = np.array([
    0.815, 0.803, 0.803, 0.808, 0.855, 0.813, 0.816, 0.827, 0.829, 0.776,
    0.786, 0.822, 0.891, 0.894, 0.894, 0.869, 0.914, 0.889, 0.885, 0.898,
    0.896, 0.86, 0.887, 0.88, 0.936, 0.913, 0.9, 0.912, 0.935, 0.928, 0.915,
    0.916, 0.929, 0.92, 0.916, 0.926
    ])

# > cat_items(summ_meth, prefix="results_meth.")
# duplicate deleted

results_meth.residuals_type = 'sweighted2'
results_meth.iterations = np.array([
    12, 3
    ])

results_meth.table_mean = np.array([
    1.44224319715775, 0.0698572427112336, 0.607345321898288, 0.973547608125426,
    0.0340120810079364, 0.0435912797271355, 0.0483424930413969,
    0.0531104241011462, 42.4038504677562, 1.60255085761448, 12.5633843785881,
    18.3306314080896, 0, 0.109033850726723, 3.35661710796797e-36,
    4.71401008973566e-75
    ]).reshape(4, 4, order='F')

results_meth.table_precision = np.array([
    8.22828526376512, -0.0347054296138766, 1.79098056245575,
    0.0327648100640521, 4.59429065633335, -1.05922877459173,
    4.34223794561173e-06, 0.289495603466561
    ]).reshape(2, 4, order='F')

results_meth.aic = -196.296056810686
results_meth.bic = -186.79494317995

results_meth.table_mean_oim = np.array([
    1.44224320770907, 0.069857238768632, 0.607345313356895, 0.973547591731571,
    0.0340453325782864, 0.0435867955242771, 0.0490089283252544,
    0.053386889034385, 42.362435567127, 1.60271563734762, 12.3925442590004,
    18.2357056075048, 0, 0.108997449531221, 2.86797597854623e-35,
    2.68762966306205e-74
    ]).reshape(4, 4, order='F')

results_meth.table_precision_oim = np.array([
    8.22828540005571, -0.0347054322904486, 1.83887205150239,
    0.0336205378385678, 4.4746372611042, -1.0322688012039,
    7.65411434417314e-06, 0.301946212204644
    ]).reshape(2, 4, order='F')

results_meth.resid = pd.read_csv(os.path.join(cur_dir,
                                              'resid_methylation.csv'))
