import numpy as np

from statsmodels.tools.testing import ParamsTableTestBunch

param_names = [
    's', 'iq', 'expr', 'tenure', 'rns', 'smsa',
    'dyear_67', 'dyear_68', 'dyear_69', 'dyear_70', 'dyear_71', 'dyear_73',
    '_cons']


est = dict(
    N=758,
    inexog_ct=10,
    exexog_ct=4,
    endog_ct=2,
    partial_ct=0,
    df_m=12,
    sdofminus=0,
    dofminus=0,
    r2=.2279825291623523,
    rmse=.3766456260250817,
    rss=107.5313411236999,
    mss=31.75480871825532,
    r2_a=.2155473484240278,
    F=37.63903370585436,
    Fp=1.04881083780e-68,
    Fdf1=12,
    Fdf2=745,
    yy=24652.2466174172,
    yyc=139.2861498419552,
    r2u=.9956380713371686,
    partialcons=0,
    cons=1,
    cdf=12.55161416131593,
    widstat=12.55161416131593,
    cd=.0675726199801665,
    idp=2.15251425210e-10,
    iddf=3,
    idstat=47.97804382236573,
    sarganp=.0013146751383334,
    sargandf=2,
    sargan=13.26833137393004,
    jp=.0013146751383334,
    jdf=2,
    j=13.26833137393004,
    ll=-335.4059158173529,
    rankV=13,
    rankxx=13,
    rankzz=15,
    r2c=.2279825291623523,
    hacsubtitleV="Statistics consistent for homoskedasticity only",
    hacsubtitleB="Estimates efficient for homoskedasticity only",
    title="IV (2SLS) estimation",
    predict="ivreg2_p",
    version="02.2.08",
    cmdline="ivreg2 lw expr tenure rns smsa dyear* (s iq=med kww age mrt)",
    cmd="ivreg2",
    model="iv",
    depvar="lw",
    partialsmall="small",
    exexog="med kww age mrt",
    inexog="expr tenure rns smsa dyear_67 dyear_68 dyear_69 dyear_70 dyear_71 dyear_73",  # noqa:E501
    insts="med kww age mrt expr tenure rns smsa dyear_67 dyear_68 dyear_69 dyear_70 dyear_71 dyear_73",  # noqa:E501
    instd="s iq",
    properties="b V",
)

params_table = np.array([
    .17242531190423,  .02073807855911,  8.3144304527925,  9.219394861e-17,
    .13177942481982,  .21307119898864, np.nan,  1.9599639845401,
    0, -.00909883103476,  .00470440157185, -1.9341101935687,
    .05309958088226, -.01831928868441,  .00012162661488, np.nan,
    1.9599639845401,                0,  .04928948974574,    .008154589406,
    6.0443864542646,  1.499796319e-09,  .03330678820126,  .06527219129022,
    np.nan,  1.9599639845401,                0,  .04221709210309,
    .00884287446052,   4.774136768712,  1.804797032e-06,  .02488537664065,
    .05954880756552, np.nan,  1.9599639845401,                0,
    -.10179345001799,  .03417647128271, -2.9784657747708,  .00289695381119,
    -.16877810285078, -.03480879718521, np.nan,  1.9599639845401,
    0,  .12611094946923,  .03092747922238,  4.0776342799379,
    .00004549625503,  .06549420406076,  .18672769487769, np.nan,
    1.9599639845401,                0, -.05961710621535,  .05529546018534,
    -1.0781555305902,  .28096435347028, -.16799421668718,  .04876000425648,
    np.nan,  1.9599639845401,                0,  .04867955999401,
    .05201609347935,  .93585574651683,  .34934746462842, -.05327010984199,
    .15062922983, np.nan,  1.9599639845401,                0,
    .15281763322545,   .0515629903814,  2.9637077309732,   .0030395682829,
    .05175602914272,  .25387923730817, np.nan,  1.9599639845401,
    0,  .17443605148569,  .05975759031645,  2.9190610023255,
    .00351087511908,  .05731332666255,  .29155877630882, np.nan,
    1.9599639845401,                0,  .09166596656323,  .05414400395495,
    1.6930031003894,  .09045487706511, -.01445433116727,  .19778626429372,
    np.nan,  1.9599639845401,                0,  .09323976497853,
    .0571819085978,  1.6305815469428,  .10297864547348, -.01883471644041,
    .20531424639747, np.nan,  1.9599639845401,                0,
    4.0335098946211,  .31542152364325,  12.787681221092,  1.921209213e-37,
    3.4152950683316,  4.6517247209107, np.nan,  1.9599639845401,
    0]).reshape(13, 9)

params_table_colnames = 'b se z pvalue ll ul df crit eform'.split()

params_table_rownames = param_names

cov = np.array([
    .00043006790232, -.00007669911732,  .00003390383154,  .00001947510761,
    -.00014211117725,  .00001984430949,  .00001313809086, -.00010726217801,
    -.0000934373525,  .00003291577926, -.00026067342405, -.00058025280789,
    .00230075400852, -.00007669911732,  .00002213139415,  4.388754481e-06,
    -8.774272042e-06,  .00005855699916, -.00001627155824, -.00001175114527,
    .00001447038262, -.00001630144788, -.00007254738147,  -.0000221423658,
    .00001715527313, -.00125964216852,  .00003390383154,  4.388754481e-06,
    .00006649732838, -.00001561989737,  .00003340985346, -6.286271714e-06,
    .00004284715305,  .00006535720207,  .00002474647562,  2.348670591e-06,
    -.00012774309302, -.00013889592824, -.00097053265371,  .00001947510761,
    -8.774272042e-06, -.00001561989737,  .00007819642872, -.00001656102532,
    -1.454183906e-06,  .00006415542548,   .0000658729123,  .00007759572764,
    .00006755177717,  .00006035533972,  .00007925563165,  .00048915076309,
    -.00014211117725,  .00005855699916,  .00003340985346, -.00001656102532,
    .00116803118934,  .00009910080232,  .00010687488928,   .0000916567869,
    -.00004794231232, -.00018139826234, -.00013904727086, -.00016941373862,
    -.00453388036786,  .00001984430949, -.00001627155824, -6.286271714e-06,
    -1.454183906e-06,  .00009910080232,  .00095650897105,  2.293607157e-06,
    -4.175180521e-06,  .00008023363682,   .0000277191399,  .00011455631826,
    7.627834100e-06,  .00071035658346,  .00001313809086, -.00001175114527,
    .00004284715305,  .00006415542548,  .00010687488928,  2.293607157e-06,
    .00305758791711,  .00082806878751,  .00084056268139,  .00086879959596,
    .00072687243235,  .00079702544855,  .00005969922435, -.00010726217801,
    .00001447038262,  .00006535720207,   .0000658729123,   .0000916567869,
    -4.175180521e-06,  .00082806878751,  .00270567398085,  .00090000327564,
    .0008652672979,  .00075215815711,  .00093055832029,  -.0011308128858,
    -.0000934373525, -.00001630144788,  .00002474647562,  .00007759572764,
    -.00004794231232,  .00008023363682,  .00084056268139,  .00090000327564,
    .00265874197707,  .00106768253686,  .00097403133741,  .00118960267354,
    .00179874271988,  .00003291577926, -.00007254738147,  2.348670591e-06,
    .00006755177717, -.00018139826234,   .0000277191399,  .00086879959596,
    .0008652672979,  .00106768253686,  .00357096960043,  .00115404290469,
    .00134956217212,  .00598978065251, -.00026067342405,  -.0000221423658,
    -.00012774309302,  .00006035533972, -.00013904727086,  .00011455631826,
    .00072687243235,  .00075215815711,  .00097403133741,  .00115404290469,
    .00293157316427,  .00171538928992,   .0048030008792, -.00058025280789,
    .00001715527313, -.00013889592824,  .00007925563165, -.00016941373862,
    7.627834100e-06,  .00079702544855,  .00093055832029,  .00118960267354,
    .00134956217212,  .00171538928992,  .00326977067089,  .00483241607215,
    .00230075400852, -.00125964216852, -.00097053265371,  .00048915076309,
    -.00453388036786,  .00071035658346,  .00005969922435,  -.0011308128858,
    .00179874271988,  .00598978065251,   .0048030008792,  .00483241607215,
    .09949073757743]).reshape(13, 13)

cov_colnames = param_names

cov_rownames = param_names


results = ParamsTableTestBunch(
    params_table=params_table,
    params_table_colnames=params_table_colnames,
    params_table_rownames=params_table_rownames,
    cov=cov,
    cov_colnames=cov_colnames,
    cov_rownames=cov_rownames,
    **est
)


est = dict(
    N=758,
    inexog_ct=10,
    exexog_ct=4,
    endog_ct=2,
    partial_ct=0,
    df_m=12,
    sdofminus=0,
    dofminus=0,
    r2=.2279825291623523,
    rmse=.3766456260250817,
    rss=107.5313411236999,
    mss=31.75480871825532,
    r2_a=.2155473484240278,
    F=40.08955571761713,
    Fp=1.50331141073e-72,
    Fdf1=12,
    Fdf2=745,
    yy=24652.2466174172,
    yyc=139.2861498419552,
    r2u=.9956380713371686,
    partialcons=0,
    cons=1,
    cdf=12.55161416131593,
    widstat=11.46142788662503,
    cd=.0675726199801665,
    idp=6.77658650925e-09,
    iddf=3,
    idstat=40.92698219921901,
    jp=.0030253131145893,
    jdf=2,
    j=11.60148136780177,
    ll=-335.4059158173529,
    rankV=13,
    rankS=15,
    rankxx=13,
    rankzz=15,
    r2c=.2279825291623523,
    hacsubtitleV="Statistics robust to heteroskedasticity",
    hacsubtitleB="Estimates efficient for homoskedasticity only",
    title="IV (2SLS) estimation",
    predict="ivreg2_p",
    version="02.2.08",
    cmdline="ivreg2 lw expr tenure rns smsa dyear* (s iq=med kww age mrt), robust",  # noqa:E501
    cmd="ivreg2",
    model="iv",
    depvar="lw",
    vcetype="Robust",
    partialsmall="small",
    exexog="med kww age mrt",
    inexog="expr tenure rns smsa dyear_67 dyear_68 dyear_69 dyear_70 dyear_71 dyear_73",  # noqa:E501
    insts="med kww age mrt expr tenure rns smsa dyear_67 dyear_68 dyear_69 dyear_70 dyear_71 dyear_73",  # noqa:E501
    instd="s iq",
    properties="b V",
)

params_table = np.array([
    .17242531190423,  .02073946970741,  8.3138727429773,  9.262847918e-17,
    .13177669821925,  .21307392558922, np.nan,  1.9599639845401,
    0, -.00909883103476,  .00488623921475,  -1.862133766863,
    .06258423744885, -.01867568391553,    .000478021846, np.nan,
    1.9599639845401,                0,  .04928948974574,  .00804979771814,
    6.1230718424932,  9.178828239e-10,  .03351217613534,  .06506680335614,
    np.nan,  1.9599639845401,                0,  .04221709210309,
    .00946363451747,  4.4609808235038,  8.158539136e-06,  .02366870928599,
    .06076547492018, np.nan,  1.9599639845401,                0,
    -.10179345001799,  .03371052765435, -3.0196338384772,   .0025308044732,
    -.16786487012036, -.03572202991563, np.nan,  1.9599639845401,
    0,  .12611094946923,  .03081138055695,  4.0929989889975,
    .0000425829578,  .06572175326364,  .18650014567481, np.nan,
    1.9599639845401,                0, -.05961710621535,  .05171372338658,
    -1.1528295065836,  .24898037077447, -.16097414155951,  .04173992912881,
    np.nan,  1.9599639845401,                0,  .04867955999401,
    .04981322391886,  .97724170740882,  .32844950437829, -.04895256484079,
    .14631168482881, np.nan,  1.9599639845401,                0,
    .15281763322545,  .04792849748217,  3.1884503219051,  .00143037585571,
    .05887950432728,  .24675576212361, np.nan,  1.9599639845401,
    0,  .17443605148569,  .06112514588945,  2.8537527223437,
    .00432061472656,   .0546329669926,  .29423913597877, np.nan,
    1.9599639845401,                0,  .09166596656323,  .05546180250571,
    1.6527765492979,  .09837634843385, -.01703716886563,  .20036910199209,
    np.nan,  1.9599639845401,                0,  .09323976497853,
    .06084900555321,  1.5323137022675,  .12544504141872, -.02602209440084,
    .21250162435791, np.nan,  1.9599639845401,                0,
    4.0335098946211,  .33503289255951,  12.039145959093,  2.212341116e-33,
    3.3768574915682,  4.6901622976741, np.nan,  1.9599639845401,
    0]).reshape(13, 9)

params_table_colnames = 'b se z pvalue ll ul df crit eform'.split()

params_table_rownames = param_names

cov = np.array([
    .00043012560374, -.00007821948167,  .00002814664043,  .00001470659468,
    -.0001813729733, -8.404214148e-06, -.00011649583596,   -.000190986044,
    -.0001267060192, -.00008672920729, -.00031350033086, -.00062509206513,
    .00258704275336, -.00007821948167,  .00002387533366,  4.911669726e-06,
    -.00001098678321,  .00006618473559, -.00002158670033,  8.107545182e-07,
    .00003255315461, -.00002143051923, -.00005975353088, -.00001402380853,
    .00001385883995, -.00142630445991,  .00002814664043,  4.911669726e-06,
    .0000647992433, -.00001977796198,  .00005110284339, -.00003232809925,
    .00003557970375,  .00008581782551,  .00002961847493,  .00001478700432,
    -.00008727552542, -.00012994173162,   -.000941201163,  .00001470659468,
    -.00001098678321, -.00001977796198,  .00008956037828, -.00003784800305,
    7.059546852e-06,   .0000815195063,  .00006348047141,  .00010852497853,
    .00009624187487,   .0001082378721,   .0000913295716,  .00074787094515,
    -.0001813729733,  .00006618473559,  .00005110284339, -.00003784800305,
    .00113639967473,   .0001331351818,  .00019039509428,  .00020009655722,
    7.191780470e-06,  .00002329093699, -.00005087978262,  .00009086571417,
    -.0049574872418, -8.404214148e-06, -.00002158670033, -.00003232809925,
    7.059546852e-06,   .0001331351818,  .00094934117183,  .00006195450043,
    .00011810217306,  .00025505395801,  .00011081126682,   .0003013467353,
    .00030676742453,  .00155300401661, -.00011649583596,  8.107545182e-07,
    .00003557970375,   .0000815195063,  .00019039509428,  .00006195450043,
    .0026743091865,  .00086135304687,  .00092017339013,  .00095567351458,
    .00088700647379,  .00102883960334,  .00031676175972,   -.000190986044,
    .00003255315461,  .00008581782551,  .00006348047141,  .00020009655722,
    .00011810217306,  .00086135304687,  .00248135727719,  .00093026821071,
    .00087773786421,  .00081079994607,   .0009428852573, -.00207087031744,
    -.0001267060192, -.00002143051923,  .00002961847493,  .00010852497853,
    7.191780470e-06,  .00025505395801,  .00092017339013,  .00093026821071,
    .0022971408709,  .00117701812528,  .00109484405896,  .00129252524214,
    .00250083573092, -.00008672920729, -.00005975353088,  .00001478700432,
    .00009624187487,  .00002329093699,  .00011081126682,  .00095567351458,
    .00087773786421,  .00117701812528,  .00373628346001,  .00123495172003,
    .00154490399913,  .00600809353497, -.00031350033086, -.00001402380853,
    -.00008727552542,   .0001082378721, -.00005087978262,   .0003013467353,
    .00088700647379,  .00081079994607,  .00109484405896,  .00123495172003,
    .00307601153718,  .00181187884386,  .00430884303329, -.00062509206513,
    .00001385883995, -.00012994173162,   .0000913295716,  .00009086571417,
    .00030676742453,  .00102883960334,   .0009428852573,  .00129252524214,
    .00154490399913,  .00181187884386,  .00370260147681,  .00534911865268,
    .00258704275336, -.00142630445991,   -.000941201163,  .00074787094515,
    -.0049574872418,  .00155300401661,  .00031676175972, -.00207087031744,
    .00250083573092,  .00600809353497,  .00430884303329,  .00534911865268,
    .11224703909679]).reshape(13, 13)

cov_colnames = param_names

cov_rownames = param_names


results_robust = ParamsTableTestBunch(
    params_table=params_table,
    params_table_colnames=params_table_colnames,
    params_table_rownames=params_table_rownames,
    cov=cov,
    cov_colnames=cov_colnames,
    cov_rownames=cov_rownames,
    **est
)


est = dict(
    N=758,
    inexog_ct=10,
    exexog_ct=4,
    endog_ct=2,
    partial_ct=0,
    df_r=745,
    df_m=12,
    sdofminus=0,
    dofminus=0,
    r2=.2279825291623523,
    rmse=.3799175840045295,
    rss=107.5313411236999,
    mss=31.75480871825532,
    r2_a=.2155473484240278,
    F=37.63903370585438,
    Fp=1.04881083780e-68,
    Fdf1=12,
    Fdf2=745,
    yy=24652.2466174172,
    yyc=139.2861498419552,
    partialcons=0,
    cons=1,
    cdf=12.55161416131593,
    widstat=12.55161416131593,
    cd=.0675726199801665,
    idp=2.15251425210e-10,
    iddf=3,
    idstat=47.97804382236573,
    sarganp=.0013146751383334,
    sargandf=2,
    sargan=13.26833137393004,
    jp=.0013146751383334,
    jdf=2,
    j=13.26833137393004,
    ll=-335.4059158173529,
    rankV=13,
    rankxx=13,
    rankzz=15,
    r2c=.2279825291623523,
    r2u=.9956380713371686,
    hacsubtitleV="Statistics consistent for homoskedasticity only",
    hacsubtitleB="Estimates efficient for homoskedasticity only",
    title="IV (2SLS) estimation",
    predict="ivreg2_p",
    version="02.2.08",
    cmdline="ivreg2 lw expr tenure rns smsa dyear* (s iq=med kww age mrt), small",  # noqa:E501
    cmd="ivreg2",
    model="iv",
    depvar="lw",
    partialsmall="small",
    small="small",
    exexog="med kww age mrt",
    inexog="expr tenure rns smsa dyear_67 dyear_68 dyear_69 dyear_70 dyear_71 dyear_73",  # noqa:E501
    insts="med kww age mrt expr tenure rns smsa dyear_67 dyear_68 dyear_69 dyear_70 dyear_71 dyear_73",  # noqa:E501
    instd="s iq",
    properties="b V",
)

params_table = np.array([
    .17242531190423,  .02091823230823,  8.2428242197305,  7.570254109e-16,
    .13135961443277,   .2134910093757,              745,  1.9631533327653,
    0, -.00909883103476,  .00474526917577,  -1.917453088061,
    .055562500369, -.01841452203205,  .00021685996252,              745,
    1.9631533327653,                0,  .04928948974574,  .00822542913447,
    5.9923304839106,  3.219967401e-09,  .03314171112698,   .0654372683645,
    745,  1.9631533327653,                0,  .04221709210309,
    .00891969338965,  4.7330205488974,  2.647391010e-06,  .02470636629795,
    .05972781790823,              745,  1.9631533327653,                0,
    -.10179345001799,  .03447336568476, -2.9528143828148,  .00324794122176,
    -.16946995275367, -.03411694728232,              745,  1.9631533327653,
    0,  .12611094946923,  .03119614930755,  4.0425165370872,
    .00005838098525,  .06486812498666,  .18735377395179,              745,
    1.9631533327653,                0, -.05961710621535,  .05577581734252,
    -1.0688701493919,  .28547438462206, -.16911358791902,  .04987937548832,
    745,  1.9631533327653,                0,  .04867955999401,
    .05246796245209,  .92779589141575,  .35381393703187, -.05432309535722,
    .15168221534524,              745,  1.9631533327653,                0,
    .15281763322545,   .0520109232025,  2.9381834394759,  .00340336072573,
    .05071221600025,  .25492305045064,              745,  1.9631533327653,
    0,  .17443605148569,  .06027671044146,  2.8939212211172,
    .00391575116799,   .0561036264944,  .29276847647697,              745,
    1.9631533327653,                0,  .09166596656323,  .05461435829744,
    1.678422477547,  .09368402236573, -.01555039294523,  .19888232607168,
    745,  1.9631533327653,                0,  .09323976497853,
    .05767865351978,  1.6165385162217,  .10640126701552, -.01999227590824,
    .20647180586531,              745,  1.9631533327653,                0,
    4.0335098946211,  .31816162176165,  12.677550083784,  1.723599587e-33,
    3.4089098465017,  4.6581099427405,              745,  1.9631533327653,
    0]).reshape(13, 9)

params_table_colnames = 'b se t pvalue ll ul df crit eform'.split()

params_table_rownames = param_names

cov = np.array([
    .0004375724429, -.00007803749118,  .00003449544203,   .0000198149417,
    -.00014459096961,  .00002019058603,  .00001336734613, -.00010913386703,
    -.00009506780295,  .00003349014856, -.00026522208782, -.00059037802467,
    .0023409013939, -.00007803749118,  .00002251757955,  4.465336774e-06,
    -8.927380145e-06,  .00005957879915, -.00001655549147, -.00001195619881,
    .00001472288594, -.00001658590268, -.00007381330893, -.00002252874265,
    .00001745462689, -.00128162250166,  .00003449544203,  4.465336774e-06,
    .00006765768445, -.00001589245933,  .00003399284419, -6.395965046e-06,
    .0000435948215,  .00006649766331,  .00002517829331,  2.389654105e-06,
    -.00012997216713, -.00014131961558, -.00098746812283,   .0000198149417,
    -8.927380145e-06, -.00001589245933,  .00007956093017, -.00001685000965,
    -1.479558927e-06,  .00006527491612,  .00006702237252,  .00007894974705,
    .00006873053302,  .00006140852014,  .00008063861583,  .00049768627977,
    -.00014459096961,  .00005957879915,  .00003399284419, -.00001685000965,
    .00118841294164,  .00010083007806,  .00010873982024,  .00009325616707,
    -.00004877888958, -.00018456360114, -.00014147359908, -.00017236995151,
    -.00461299505884,  .00002019058603, -.00001655549147, -6.395965046e-06,
    -1.479558927e-06,  .00010083007806,  .00097319973162,  2.333629833e-06,
    -4.248036020e-06,  .00008163368686,  .00002820282959,  .00011655528757,
    7.760937245e-06,  .00072275206747,  .00001336734613, -.00001195619881,
    .0000435948215,  .00006527491612,  .00010873982024,  2.333629833e-06,
    .00311094180023,  .00084251830998,  .00085523021811,  .00088395985737,
    .00073955611238,  .00081093327517,  .00006074095578, -.00010913386703,
    .00001472288594,  .00006649766331,  .00006702237252,  .00009325616707,
    -4.248036020e-06,  .00084251830998,  .00275288708387,  .00091570803079,
    .00088036592189,  .00076528306455,  .00094679625072, -.00115054519119,
    -.00009506780295, -.00001658590268,  .00002517829331,  .00007894974705,
    -.00004877888958,  .00008163368686,  .00085523021811,  .00091570803079,
    .00270513613238,  .00108631323884,  .00099102785739,    .001210360841,
    .00183013017674,  .00003349014856, -.00007381330893,  2.389654105e-06,
    .00006873053302, -.00018456360114,  .00002820282959,  .00088395985737,
    .00088036592189,  .00108631323884,  .00363328182164,  .00117418056612,
    .00137311157915,   .0060943003149, -.00026522208782, -.00002252874265,
    -.00012997216713,  .00006140852014, -.00014147359908,  .00011655528757,
    .00073955611238,  .00076528306455,  .00099102785739,  .00117418056612,
    .00298272813224,  .00174532225739,   .0048868116328, -.00059037802467,
    .00001745462689, -.00014131961558,  .00008063861583, -.00017236995151,
    7.760937245e-06,  .00081093327517,  .00094679625072,    .001210360841,
    .00137311157915,  .00174532225739,  .00332682707186,  .00491674011099,
    .0023409013939, -.00128162250166, -.00098746812283,  .00049768627977,
    -.00461299505884,  .00072275206747,  .00006074095578, -.00115054519119,
    .00183013017674,   .0060943003149,   .0048868116328,  .00491674011099,
    .101226817562]).reshape(13, 13)

cov_colnames = param_names

cov_rownames = param_names


# not autogenerated
# calculated with `ivendog` after ivreg2
hausman = dict(
    df_r=743,
    df=2,
    WHFp=1.47099195224e-16,
    WHF=38.30408858936179,
    DWHp=4.12270104038e-16,
    DWH=70.84970589405181
)

results_small = ParamsTableTestBunch(
    params_table=params_table,
    params_table_colnames=params_table_colnames,
    params_table_rownames=params_table_rownames,
    cov=cov,
    cov_colnames=cov_colnames,
    cov_rownames=cov_rownames,
    hausman=hausman,
    **est
)


est = dict(
    N=758,
    inexog_ct=10,
    exexog_ct=4,
    endog_ct=2,
    partial_ct=0,
    df_r=745,
    df_m=12,
    sdofminus=0,
    dofminus=0,
    r2=.2279825291623523,
    rmse=.3799175840045295,
    rss=107.5313411236999,
    mss=31.75480871825532,
    r2_a=.2155473484240278,
    F=40.08955571761724,
    Fp=1.50331141073e-72,
    Fdf1=12,
    Fdf2=745,
    yy=24652.2466174172,
    yyc=139.2861498419552,
    partialcons=0,
    cons=1,
    cdf=12.55161416131593,
    widstat=11.46142788662503,
    cd=.0675726199801665,
    idp=6.77658650925e-09,
    iddf=3,
    idstat=40.92698219921901,
    jp=.0030253131145893,
    jdf=2,
    j=11.60148136780177,
    ll=-335.4059158173529,
    rankV=13,
    rankS=15,
    rankxx=13,
    rankzz=15,
    r2c=.2279825291623523,
    r2u=.9956380713371686,
    hacsubtitleV="Statistics robust to heteroskedasticity",
    hacsubtitleB="Estimates efficient for homoskedasticity only",
    title="IV (2SLS) estimation",
    predict="ivreg2_p",
    version="02.2.08",
    cmdline="ivreg2 lw expr tenure rns smsa dyear* (s iq=med kww age mrt), small robust",  # noqa:E501
    cmd="ivreg2",
    model="iv",
    depvar="lw",
    vcetype="Robust",
    partialsmall="small",
    small="small",
    exexog="med kww age mrt",
    inexog="expr tenure rns smsa dyear_67 dyear_68 dyear_69 dyear_70 dyear_71 dyear_73",  # noqa:E501
    insts="med kww age mrt expr tenure rns smsa dyear_67 dyear_68 dyear_69 dyear_70 dyear_71 dyear_73",  # noqa:E501
    instd="s iq",
    properties="b V",
)

params_table = np.array([
    .17242531190423,  .02091963554158,  8.2422713130704,  7.602390656e-16,
    .13135685967055,  .21349376413792,              745,  1.9631533327653,
    0, -.00909883103476,  .00492868646034, -1.8460965427549,
    .06527471189517, -.01877459828554,  .00057693621602,              745,
    1.9631533327653,                0,  .04928948974574,  .00811972711081,
    6.0703382112603,  2.032123371e-09,  .03334922040701,  .06522975908447,
    745,  1.9631533327653,                0,  .04221709210309,
    .0095458460509,  4.4225615915015,  .00001120706314,   .0234771326142,
    .06095705159197,              745,  1.9631533327653,                0,
    -.10179345001799,   .0340033743578, -2.9936278954812,  .00284801771574,
    -.16854728771377, -.03503961232222,              745,  1.9631533327653,
    0,  .12611094946923,  .03107904208149,   4.057748920915,
    .00005477891606,   .0650980244278,  .18712387451065,              745,
    1.9631533327653,                0, -.05961710621535,  .05216296564028,
    -1.1429010119263,  .25344683532742, -.16202100605898,  .04278679362828,
    745,  1.9631533327653,                0,  .04867955999401,
    .05024595634484,  .96882542467533,  .33294671206223, -.04996095666234,
    .14732007665036,              745,  1.9631533327653,                0,
    .15281763322545,  .04834485710231,  3.1609904834768,  .00163599246861,
    .05790926588299,  .24772600056791,              745,  1.9631533327653,
    0,  .17443605148569,  .06165614610562,  2.8291753945643,
    .00479228267652,  .05339558277297,   .2954765201984,              745,
    1.9631533327653,                0,  .09166596656323,  .05594360469515,
    1.638542368922,  .10173082236229, -.01815990744096,  .20149184056742,
    745,  1.9631533327653,                0,  .09323976497853,
    .06137760691084,  1.5191169820938,  .12915730016675, -.02725388858564,
    .21373341854271,              745,  1.9631533327653,                0,
    4.0335098946211,  .33794335658841,  11.935461419748,  3.577808889e-30,
    3.3700752678487,  4.6969445213936,              745,  1.9631533327653,
    0]).reshape(13, 9)

params_table_colnames = 'b se t pvalue ll ul df crit eform'.split()

params_table_rownames = param_names

cov = np.array([
    .00043763115119, -.00007958438537,  .00002863778986,  .00001496321982,
    -.00018453787082, -8.550864865e-06,  -.0001185286492, -.00019431868638,
    -.00012891699672, -.00008824260285, -.00031897080643,  -.0006359997119,
    .00263218578127, -.00007958438537,  .00002429195022,  4.997376715e-06,
    -.00001117849889,  .00006733963701, -.00002196338101,  8.249019125e-07,
    .00003312119623,  -.0000218044746, -.00006079620995, -.00001426851928,
    .00001410067206, -.00145119299411,  .00002863778986,  4.997376715e-06,
    .00006592996835, -.00002012308078,  .00005199457086, -.00003289221373,
    .00003620055764,  .00008731531776,  .00003013530738,  .00001504503258,
    -.00008879845405, -.00013220917123, -.00095762480745,  .00001496321982,
    -.00001117849889, -.00002012308078,  .00009112317683,   -.000038508438,
    7.182733576e-06,  .00008294199433,  .00006458818434,  .00011041870299,
    .00009792126329,  .00011012658665,  .00009292324197,  .00076092104218,
    -.00018453787082,  .00006733963701,  .00005199457086,   -.000038508438,
    .00115622946772,  .00013545834604,  .00019371742478,    .000203588175,
    7.317274626e-06,  .00002369735603, -.00005176761775,  .00009245129039,
    -.00504399373059, -8.550864865e-06, -.00002196338101, -.00003289221373,
    7.182733576e-06,  .00013545834604,   .0009659068567,  .00006303558567,
    .00012016301635,  .00025950456399,  .00011274488624,  .00030660513471,
    .00031212041314,  .00158010341555,  -.0001185286492,  8.249019125e-07,
    .00003620055764,  .00008294199433,  .00019371742478,  .00006303558567,
    .00272097498439,   .0008763833685,    .000936230107,  .00097234969671,
    .0009024844391,  .00104679250917,  .00032228914613, -.00019431868638,
    .00003312119623,  .00008731531776,  .00006458818434,    .000203588175,
    .00012016301635,   .0008763833685,  .00252465612901,  .00094650107881,
    .0008930540954,  .00082494813305,  .00095933828863, -.00210700630956,
    -.00012891699672,  -.0000218044746,  .00003013530738,  .00011041870299,
    7.317274626e-06,  .00025950456399,    .000936230107,  .00094650107881,
    .00233722520824,   .0011975566966,  .00111394872039,  .00131507937389,
    .00254447447521, -.00008824260285, -.00006079620995,  .00001504503258,
    .00009792126329,  .00002369735603,  .00011274488624,  .00097234969671,
    .0008930540954,   .0011975566966,   .0038014803526,  .00125650121313,
    .00157186205549,  .00611293275102, -.00031897080643, -.00001426851928,
    -.00008879845405,  .00011012658665, -.00005176761775,  .00030660513471,
    .0009024844391,  .00082494813305,  .00111394872039,  .00125650121313,
    .00312968690629,  .00184349552167,   .0043840308983,  -.0006359997119,
    .00001410067206, -.00013220917123,  .00009292324197,  .00009245129039,
    .00031212041314,  .00104679250917,  .00095933828863,  .00131507937389,
    .00157186205549,  .00184349552167,   .0037672106301,  .00544245897816,
    .00263218578127, -.00145119299411, -.00095762480745,  .00076092104218,
    -.00504399373059,  .00158010341555,  .00032228914613, -.00210700630956,
    .00254447447521,  .00611293275102,   .0043840308983,  .00544245897816,
    .11420571226224]).reshape(13, 13)

cov_colnames = param_names

cov_rownames = param_names


results_small_robust = ParamsTableTestBunch(
    params_table=params_table,
    params_table_colnames=params_table_colnames,
    params_table_rownames=params_table_rownames,
    cov=cov,
    cov_colnames=cov_colnames,
    cov_rownames=cov_rownames,
    **est
)


est = dict(
    N=758,
    inexog_ct=10,
    exexog_ct=4,
    endog_ct=2,
    partial_ct=0,
    df_m=12,
    sdofminus=0,
    dofminus=0,
    r2=.2168305947462866,
    rmse=.3793562300139549,
    rss=109.0846511318037,
    mss=30.20149871015154,
    r2_a=.2042157855341463,
    F=41.97598961240392,
    Fp=1.89290505854e-75,
    Fdf1=12,
    Fdf2=745,
    yy=24652.2466174172,
    yyc=139.2861498419552,
    r2u=.995575062475047,
    partialcons=0,
    cons=1,
    cdf=12.55161416131593,
    widstat=11.46142788662503,
    cd=.0675726199801665,
    idp=6.77658650925e-09,
    iddf=3,
    idstat=40.92698219921901,
    jp=.0030253131145893,
    jdf=2,
    j=11.60148136780177,
    ll=-340.8414755627023,
    rankV=13,
    rankS=15,
    rankxx=13,
    rankzz=15,
    r2c=.2168305947462866,
    hacsubtitleV="Statistics robust to heteroskedasticity",
    hacsubtitleB="Estimates efficient for arbitrary heteroskedasticity",
    title="2-Step GMM estimation",
    predict="ivreg2_p",
    version="02.2.08",
    cmdline="ivreg2 lw expr tenure rns smsa dyear* (s iq=med kww age mrt), gmm2s robust",  # noqa:E501
    cmd="ivreg2",
    model="gmm2s",
    depvar="lw",
    vcetype="Robust",
    partialsmall="small",
    exexog="med kww age mrt",
    inexog="expr tenure rns smsa dyear_67 dyear_68 dyear_69 dyear_70 dyear_71 dyear_73",  # noqa:E501
    insts="med kww age mrt expr tenure rns smsa dyear_67 dyear_68 dyear_69 dyear_70 dyear_71 dyear_73",  # noqa:E501
    instd="s iq",
    properties="b V",
)

params_table = np.array([
    .17579576800916,  .02067662557145,  8.5021498020418,  1.861114268e-17,
    .13527032656731,  .21632120945101, np.nan,  1.9599639845401,
    0, -.00928615655484,  .00488241935929, -1.9019579989933,
    .05717664576009, -.01885552265647,  .00028320954679, np.nan,
    1.9599639845401,                0,   .0502827590727,  .00804384217519,
    6.251087226427,  4.076051363e-10,  .03451711811201,  .06604840003339,
    np.nan,  1.9599639845401,                0,  .04252138311207,
    .00945488880069,  4.4972906618407,  6.882486994e-06,   .0239901415849,
    .06105262463925, np.nan,  1.9599639845401,                0,
    -.10409306761385,  .03352385821237,  -3.105044382255,  .00190250475151,
    -.16979862233293, -.03838751289477, np.nan,  1.9599639845401,
    0,  .12475123235604,  .03077474836143,  4.0536881371349,
    .00005041641801,  .06443383393436,  .18506863077773, np.nan,
    1.9599639845401,                0, -.05304317352459,  .05146091261443,
    -1.0307468490116,  .30265954893659, -.15390470886044,  .04781836181126,
    np.nan,  1.9599639845401,                0,  .04595459037414,
    .04957352345681,  .92699867126001,  .35392722417214, -.05120773018796,
    .14311691093625, np.nan,  1.9599639845401,                0,
    .15548006234452,     .04763105506,   3.264258206094,  .00109751095685,
    .06212490988128,  .24883521480777, np.nan,  1.9599639845401,
    0,  .16698745539298,  .06100058345996,  2.7374730850337,
    .00619131861175,  .04742850877554,  .28654640201043, np.nan,
    1.9599639845401,                0,  .08464846645323,  .05540348923564,
    1.5278544297672,  .12654868468826, -.02394037706648,  .19323730997294,
    np.nan,  1.9599639845401,                0,  .09960684400937,
    .06070338085689,  1.6408780302402,  .10082273637942, -.01936959620995,
    .2185832842287, np.nan,  1.9599639845401,                0,
    4.0039243729942,  .33484233500232,  11.957640819123,  5.922056626e-33,
    3.3476454558904,  4.6602032900981, np.nan,  1.9599639845401,
    0]).reshape(13, 9)

params_table_colnames = 'b se z pvalue ll ul df crit eform'.split()

params_table_rownames = param_names

cov = np.array([
    .00042752284502, -.00007792910934,  .00002799053689,  .00001411255161,
    -.00017627354771, -6.164167361e-06, -.00011238067476,  -.0001840592946,
    -.00012075971225, -.00008045016526, -.00030956302647, -.00062213915849,
    .0025864317125, -.00007792910934,   .0000238380188,  4.908457545e-06,
    -.0000109083863,  .00006550360735, -.00002187678826,  4.129956698e-08,
    .00003161755605, -.00002236410253, -.00006047183643,  -.0000144130349,
    .00001326368633, -.00142544425011,  .00002799053689,  4.908457545e-06,
    .00006470339694, -.00001976276105,  .00005093819277,  -.0000323622135,
    .00003452531659,  .00008555059005,   .0000288422901,  .00001508898973,
    -.00008682961428, -.00013087868262, -.00093791708189,  .00001411255161,
    -.0000109083863, -.00001976276105,  .00008939492223, -.00003640760589,
    7.669817608e-06,  .00008322349071,  .00006546263505,  .00011054793547,
    .00009771882719,  .00010901321979,   .0000926582973,  .00074577978354,
    -.00017627354771,  .00006550360735,  .00005093819277, -.00003640760589,
    .00112384906944,  .00012782990916,   .0001752536517,  .00018281146997,
    -.00001062129812,   .0000105829103, -.00005745271672,  .00007901951626,
    -.00493822443862, -6.164167361e-06, -.00002187678826,  -.0000323622135,
    7.669817608e-06,  .00012782990916,  .00094708513671,  .00005587818268,
    .0001108109152,  .00024772966236,  .00010526192189,  .00029836672951,
    .0003020559345,  .00155999842113, -.00011238067476,  4.129956698e-08,
    .00003452531659,  .00008322349071,   .0001752536517,  .00005587818268,
    .00264822552711,  .00084013008538,  .00089372787896,  .00094462335409,
    .00088393735147,  .00100740924129,  .00036769011237,  -.0001840592946,
    .00003161755605,  .00008555059005,  .00006546263505,  .00018281146997,
    .0001108109152,  .00084013008538,  .00245753422792,  .00090550111616,
    .00086043845508,  .00080197738555,  .00092623266791, -.00204303139567,
    -.00012075971225, -.00002236410253,   .0000288422901,  .00011054793547,
    -.00001062129812,  .00024772966236,  .00089372787896,  .00090550111616,
    .00226871740613,  .00116169566043,  .00108859648529,  .00127118636737,
    .00254572878148, -.00008045016526, -.00006047183643,  .00001508898973,
    .00009771882719,   .0000105829103,  .00010526192189,  .00094462335409,
    .00086043845508,  .00116169566043,  .00372107118246,  .00122563410541,
    .00153681917159,  .00601243235865, -.00030956302647,  -.0000144130349,
    -.00008682961428,  .00010901321979, -.00005745271672,  .00029836672951,
    .00088393735147,  .00080197738555,  .00108859648529,  .00122563410541,
    .00306954661948,  .00181011399651,  .00430171295221, -.00062213915849,
    .00001326368633, -.00013087868262,   .0000926582973,  .00007901951626,
    .0003020559345,  .00100740924129,  .00092623266791,  .00127118636737,
    .00153681917159,  .00181011399651,  .00368490044746,  .00539258837124,
    .0025864317125, -.00142544425011, -.00093791708189,  .00074577978354,
    -.00493822443862,  .00155999842113,  .00036769011237, -.00204303139567,
    .00254572878148,  .00601243235865,  .00430171295221,  .00539258837124,
    .11211938930981]).reshape(13, 13)

cov_colnames = param_names

cov_rownames = param_names


results_gmm2s_robust = ParamsTableTestBunch(
    params_table=params_table,
    params_table_colnames=params_table_colnames,
    params_table_rownames=params_table_rownames,
    cov=cov,
    cov_colnames=cov_colnames,
    cov_rownames=cov_rownames,
    **est
)
