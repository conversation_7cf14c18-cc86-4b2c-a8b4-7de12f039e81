@echo off
REM Activate virtual environment
call .\venv\Scripts\activate
echo Virtual environment activated.

REM Change to src directory
cd .\src\
echo Changed to src directory.

REM Run Python application in a new window
start cmd /k "python -m Infrared_analysis.app"
echo Python application started.
timeout /t 60 /nobreak >nul

REM Open browser with the Dash URL in a new window
echo Python application is running. Opening browser...
start http://127.0.0.1:8050/
echo Browser window with Dash URL opened.