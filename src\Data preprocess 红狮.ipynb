import numpy as np
import pandas as pd
import os
from keys import Cols, DataDir
from utils import *

from sklearn.linear_model import LinearRegression
from datetime import datetime,time,date
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import matplotlib.cm as cm
import matplotlib.colors as mcolors
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from warnings import filterwarnings

# 中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

source_folder = os.path.join(
    DataDir.raw,
    '红狮数据-202504',
    
)
target_folder = os.path.join(
    DataDir.interim,
    'Formatted'
)
sub_folder_list = os.listdir(source_folder)
sub_folder_list

with gzip.open(
    os.path.join(
        source_folder,
        'Electrolyzers data dict with list 20250423 gzip'
    ),
    'rb'
) as f:
    df_ele_list = pickle.load(f)

df_eles = {}
for key in df_ele_list.keys():
    df_eles[key] = pd.concat(
        df_ele_list[key]
    )

for key in df_eles.keys():
    df_eles[key] = df_eles[key].rename(
        columns = {
            '时间':Cols.date_time,
            f'{key}#系统压力':Cols.pressure,
            f'{key}#电压':Cols.voltage,
            f'{key}#电流':Cols.current,
            f'{key}#碱液流量':Cols.lye_flow,
            f'{key}#碱温':Cols.lye_temp,
            f'{key}#氧槽温':Cols.temp_O,
            f'{key}#氢槽温':Cols.temp_H,
            f'{key}#氢中氧':Cols.OTH,
            f'{key}#氧中氢':Cols.HTO,
        }
    ).dropna(how='all').fillna(0)
    df_eles[key][Cols.date_time] = df_eles[key][Cols.date_time].apply(pd.to_datetime)


list(df_eles.keys())

for key in df_eles.keys():
    df_eles[key] = df_eles[key].drop_duplicates(subset=[Cols.date_time]).sort_values(by =Cols.date_time)
    for col in list(df_eles[1].columns)[1:]:
        df_eles[key] = clip_abnormal_col(df_eles[key] ,col)

for key in df_eles.keys():
    df_eles[key][Cols.cell_voltage] = df_eles[key][Cols.voltage]/214
    df_eles[key][Cols.current_density] = df_eles[key][Cols.current] / (np.pi*(1.832/2)**2)
    df_eles[key][Cols.temp_out] = (df_eles[key][Cols.temp_O]+df_eles[key][Cols.temp_H])/2

for key in df_eles.keys():
    df_eles[key] = sort_columns(df_eles[key])

df_eles[2]

df_eles[key].iloc[-1][Cols.date_time].strftime('%Y-%m-%d')

df_eles[key].iloc[-1][Cols.date_time].strftime('%Y-%m-%d')





'Hi1_红狮_No{}_{}_{} gzip'.format(key,df_eles[key].iloc[0][Cols.date_time].strftime('%Y%m%d'),df_eles[key].iloc[-1][Cols.date_time].strftime('%Y-%m-%d'))

'Hi1_红狮_No{}_{}_20250415 gzip'.format(key,df_eles[key].iloc[0][Cols.date_time].strftime('%Y%m%d'))

for key in df_eles.keys():
    # 预存储合并后结果
    with gzip.open(
        os.path.join(
            target_folder,
            'Hi1_红狮_No{}_{}_{} gzip'.format(key,df_eles[key].iloc[0][Cols.date_time].strftime('%Y%m%d'),df_eles[key].iloc[-1][Cols.date_time].strftime('%Y-%m-%d'))
        ),
        'wb'

    ) as f:
        pickle.dump(df_eles[key],f)

for key in df_eles.keys():
    df_eles[key].to_excel(
        os.path.join(
            target_folder,
            'Hi1_红狮_No{}_{}_{}.xlsx'.format(key,df_eles[key].iloc[0][Cols.date_time].strftime('%Y%m%d'),df_eles[key].iloc[-1][Cols.date_time].strftime('%Y-%m-%d'))
        )
    )

df_eles[5]

cur_folder = os.path.join(
    source_folder,
    'All'
)
df_list = []
for file in os.listdir(cur_folder):
    df_cur = pd.read_excel(
        os.path.join(
            cur_folder,
            file
        )
    )
    df_list.append(df_cur)

df_ele_list = {}
for key in range(1,6):
    df_ele_list[key] = []
    for file in os.listdir(cur_folder):
        if '{}#'.format(key) in file:
            df_cur = pd.read_excel(
                os.path.join(
                    cur_folder,
                    file
                )
            )
            df_ele_list[key].append(df_cur)



with gzip.open(
    os.path.join(
        source_folder,
        'Electrolyzers data dict with list 20250423 gzip'
    ),
    'wb'
) as f:
    pickle.dump(df_ele_list,f)











source_folder = os.path.join(
    DataDir.raw,
    'Formatted'
)
target_folder = os.path.join(
    DataDir.interim,
    'Formatted'
)
file_list = os.listdir(source_folder)
print('当前文件夹：'+source_folder+'，包含文件：')
file_list

file = list(filter(None.__ne__,list(set([f if '红狮' in f else None for f in file_list]))))
print(file)

df_3 = pd.read_excel(
    os.path.join(
        source_folder,
        file[0]
    )
)
df_4 = pd.read_excel(
    os.path.join(
        source_folder,
        file[1]
    )
)

df_3.columns

df_3 = df_3.rename(
    columns = {
        '时间':Cols.date_time,
        '3#系统压力':Cols.pressure,
        '3#电压':Cols.voltage,
        '3#电流':Cols.current,
        '3#碱液流量':Cols.lye_flow,
        '3#碱温':Cols.lye_temp,
        '3#氧槽温':Cols.temp_O,
        '3#氢槽温':Cols.temp_H,
        '3#氢中氧':Cols.OTH,
        '3#氧中氢':Cols.HTO,
    }
).dropna(how='all').fillna(0)

df_4 = df_4.rename(
    columns = {
        '时间':Cols.date_time,
        '4#系统压力':Cols.pressure,
        '4#电压':Cols.voltage,
        '4#电流':Cols.current,
        '4#碱液流量':Cols.lye_flow,
        '4#碱温':Cols.lye_temp,
        '4#氧槽温':Cols.temp_O,
        '4#氢槽温':Cols.temp_H,
        '4#氢中氧':Cols.OTH,
        '4#氧中氢':Cols.HTO,
    }
).dropna(how='all').fillna(0)

df_3[Cols.date_time] = df_3[Cols.date_time].apply(pd.to_datetime)
df_3[Cols.cell_voltage] = df_3[Cols.voltage]/214
df_3[Cols.current_density] = df_3[Cols.current] / (np.pi*(1.832/2)**2)
df_3[Cols.temp_out] = (df_3[Cols.temp_O]+df_3[Cols.temp_H])/2

df_4[Cols.date_time] = df_4[Cols.date_time].apply(pd.to_datetime)
df_4[Cols.cell_voltage] = df_4[Cols.voltage]/214
df_4[Cols.current_density] = df_4[Cols.current] / (np.pi*(1.832/2)**2)
df_4[Cols.temp_out] = (df_4[Cols.temp_O]+df_4[Cols.temp_H])/2



with gzip.open(
    os.path.join(
        target_folder,
        'Hi1_红狮_No3_20250402 gzip'
    ),
    'wb'
) as f:
    pickle.dump(df_3,f)

with gzip.open(
    os.path.join(
        target_folder,
        'Hi1_红狮_No4_20250402 gzip'
    ),
    'wb'
) as f:
    pickle.dump(df_4,f)

from figure_plotter.figure_plotter import * 
from figure_plotter.data_base import *
project = '红狮'
data_set,key_list = load_project_multi_stack_data(project=project) # 读取出所有的数据

plt.plot(
    data_set[3][Cols.date_time],
    data_set[3][Cols.lye_temp]
)





plot_project_multi_overall_conditions(project, data_set, key_list,HTO_lim = [0,4])



