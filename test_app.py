#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Test script for the Infrared Analysis application
"""

import sys
import os

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    print("Testing imports...")
    
    # Test basic imports
    import dash
    print(f"✓ Dash version: {dash.__version__}")
    
    import plotly
    print(f"✓ Plotly version: {plotly.__version__}")
    
    import pandas as pd
    print(f"✓ Pandas version: {pd.__version__}")
    
    import numpy as np
    print(f"✓ NumPy version: {np.__version__}")
    
    # Test infrared analysis imports
    from Infrared_analysis.utils_image import read_infrared_image, multi_gaussian
    print("✓ Infrared utils imported successfully")
    
    from Infrared_analysis.app import app
    print("✓ Infrared app imported successfully")
    
    # Test data directory
    data_dir = os.path.join(os.path.dirname(__file__), 'data', 'raw', 'Infrared')
    if os.path.exists(data_dir):
        folders = [f for f in os.listdir(data_dir) if os.path.isdir(os.path.join(data_dir, f))]
        print(f"✓ Found {len(folders)} data folders in Infrared directory")
        if folders:
            print(f"  Sample folders: {folders[:3]}")
    else:
        print("✗ Infrared data directory not found")
    
    print("\n" + "="*50)
    print("All tests passed! Starting the application...")
    print("="*50)
    print("Open your browser and go to: http://127.0.0.1:8050/")
    print("Press Ctrl+C to stop the application")
    print("="*50)
    
    # Start the application
    app.run_server(debug=True, host='127.0.0.1', port=8050)
    
except ImportError as e:
    print(f"✗ Import error: {e}")
    sys.exit(1)
except Exception as e:
    print(f"✗ Error: {e}")
    sys.exit(1)
